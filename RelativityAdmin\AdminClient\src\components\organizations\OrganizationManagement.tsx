import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Search,
  Building2,
  Users,
  CreditCard,
  MapPin,
  Trash2,
  Eye,
  Settings,
} from "lucide-react";
import OrganizationDetailsModal from "./OrganizationDetailsModal";
import OrganizationManageModal from "./OrganizationManageModal";
import AddOrganizationModal from "./AddOrganizationModal";
import { useTranslation } from "react-i18next";
import {
  useOrganizations,
  useUpdateOrganization,
  useDeleteOrganization,
} from "@/hooks/useOrganizations";
import { LoadingSpinner } from "../layout/LoadingSpinner";
import { Organization, OrganizationStatus } from "@/lib/Schemas/Organization";
import { useLicenses } from "@/hooks/useLicensePackage";
import { useDecodedJwt } from "@/hooks/useDecodedJwt";
import ConfirmDialog from "../confirmation/ConfirmDialog";
import { hasPermission } from "@relativity/shared";
import { useLanguage } from "@relativity/shared/dist/i18n/LanguageProvider";

const OrganizationManagement: React.FC = () => {
  // const { t } = useTranslation();
  const { t } = useLanguage();
  const jwtPayload = useDecodedJwt();
  const permissions = jwtPayload?.permissionList || [];
  const canViewOrg = hasPermission(permissions, "organizations", "read");
  const canAddOrg = hasPermission(permissions, "organizations", "create");
  const canEditOrg = hasPermission(permissions, "organizations", "update");
  const canDeleteOrg = hasPermission(permissions, "organizations", "delete");

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [manageModalOpen, setManageModalOpen] = useState(false);
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [orgToDelete, setOrgToDelete] = useState<string | null>(null);

  const { data: organizations, isLoading, isError } = useOrganizations();
  const { mutate: updateOrganization } = useUpdateOrganization();
  const { mutate: deleteOrganization } = useDeleteOrganization();
  const { data: licensePackages = [] } = useLicenses();

  const filteredOrganizations = organizations?.filter(
    (org) =>
      org.organizationName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      org.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
      org.address.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
      org.address.state.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: OrganizationStatus) => {
    switch (status) {
      case OrganizationStatus.Active:
        return "bg-green-100 text-green-800";
      case OrganizationStatus.Pending:
        return "bg-yellow-100 text-yellow-800";
      case OrganizationStatus.Suspended:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-800";
    }
  };

  const handleViewDetails = (org: Organization) => {
    setSelectedOrg(org);
    setDetailsModalOpen(true);
  };

  const handleManage = (org: Organization) => {
    setSelectedOrg(org);
    setManageModalOpen(true);
  };

  const handleDeleteOrganization = (id: string) => {
    deleteOrganization(id);
  };

  const handleDelete = (id: string) => {
    setOrgToDelete(id);
    setConfirmOpen(true);
  };

  const handleConfirmDelete = () => {
    if (orgToDelete) {
      deleteOrganization(orgToDelete);
      setConfirmOpen(false);
      setOrgToDelete(null);
    }
  };

  const handleUpdateOrganization = (updatedOrg: Organization) => {
    debugger;
    updateOrganization({ ...updatedOrg, _id: updatedOrg._id });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-foreground">
            {t("organizations.title")}
          </h2>
          <p className="text-muted-foreground mt-2">
            {t("organizations.subtitle")}
          </p>
        </div>
        {canAddOrg && (
          <Button
            className="bg-primary hover:bg-primary/90 text-primary-foreground"
            onClick={() => setAddModalOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            {t("organizations.addOrganizations")}
          </Button>
        )}
      </div>

      {!canViewOrg ? (
        <div className="flex items-center justify-center h-[50vh] text-lg text-muted-foreground">
          {t("No Permission to view organizations")}
        </div>
      ) : isLoading ? (
        <div className="flex items-center justify-center h-[50vh]">
          <LoadingSpinner />
        </div>
      ) : isError ? (
        <p>{t("organizations.error")}</p>
      ) : (
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t("organizations.search")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredOrganizations?.map((org) => (
                <div
                  key={org._id}
                  className="border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="bg-primary/10 p-2 rounded-lg">
                        <Building2 className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-foreground">
                          {org.organizationName}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {org.domain}
                        </p>
                        <div className="flex items-center space-x-1 mt-1">
                          <MapPin className="h-3 w-3 text-muted-foreground" />
                          <p className="text-xs text-muted-foreground">
                            {org.address.city}, {org.address.state}
                          </p>
                        </div>
                      </div>
                    </div>
                    <Badge className={getStatusColor(org.status)}>
                      {t(
                        `organizations.status.${typeof org?.status === "string"
                          ? org.status.toLowerCase()
                          : ""
                        }`
                      )}
                    </Badge>
                  </div>

                  <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">
                        {org.users}/{org.numberOfLicenses}{" "}
                        {t("organizations.users")}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">
                        ${org.monthlyRevenue.toLocaleString()}/
                        {t("organizations.month")}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(org)}>
                        <Eye className="w-4 h-4" />

                      </Button>
                      {canEditOrg && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleManage(org)}>
                          <Settings className="w-4 h-4" />
                        </Button>
                      )}

                      {canDeleteOrg && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(org._id)}>
                          <Trash2 className="w-4 h-4 text-red-600" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <OrganizationDetailsModal
        organization={selectedOrg}
        open={detailsModalOpen}
        onOpenChange={setDetailsModalOpen}
      />

      <OrganizationManageModal
        organization={selectedOrg}
        open={manageModalOpen}
        onOpenChange={setManageModalOpen}
        onUpdate={handleUpdateOrganization}
        licensePackages={licensePackages}
      />

      <AddOrganizationModal
        open={addModalOpen}
        onOpenChange={setAddModalOpen}
      />

      <ConfirmDialog
        open={confirmOpen}
        onCancel={() => setConfirmOpen(false)}
        onConfirm={handleConfirmDelete}
        title={t("organizations.delete")}
        description={t("organizations.deleteMessage")}
        confirmText={t("organizations.deleteConfirm")}
        cancelText={t("common.cancel")}
      />
    </div>
  );
};

export default OrganizationManagement;
