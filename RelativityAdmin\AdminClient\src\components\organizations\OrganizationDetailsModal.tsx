import React from "react";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Building2,
  Users,
  CreditCard,
  Calendar,
  Globe,
  Mail,
} from "lucide-react";
import {
  OrganizationDetailsModalProps,
  OrganizationStatus,
} from "@/lib/Schemas/Organization";
import { useLanguage } from "@relativity/shared/dist/i18n/LanguageProvider";

const OrganizationDetailsModal: React.FC<OrganizationDetailsModalProps> = ({
  organization,
  open,
  onOpenChange,
}) => {
  const { t } = useLanguage();
  if (!organization) return null;

  const getStatusColor = (status: OrganizationStatus) => {
    switch (status) {
      case OrganizationStatus.Active:
        return "bg-green-100 text-green-800";
      case OrganizationStatus.Pending:
        return "bg-yellow-100 text-yellow-800";
      case OrganizationStatus.Suspended:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-800";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5 text-blue-600" />
            <span>{organization.organizationName} - {t('organizations.details')}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status Badge */}
          <div className="flex justify-between items-start">
            <Badge className={getStatusColor(organization.status)}>
              {organization.status}
            </Badge>
            <div className="text-right">
              <p className="text-sm text-500">{t('organizations.orgId')}</p>
              <p className="font-mono text-sm">{organization._id}</p>
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Globe className="h-4 w-4 text-400" />
                <div>
                  <p className="text-sm font-medium text-900">{t('organizations.domain')}</p>
                  <p className="text-sm text-600">{organization.domain}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-400" />
                <div>
                  <p className="text-sm font-medium text-900">{t('organizations.contactEmail')}</p>
                  <p className="text-sm text-600">
                    admin@{organization.domain}
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-400" />
                <div>
                  <p className="text-sm font-medium text-900">{t('organizations.createdDate')}</p>
                  <p className="text-sm text-600">January 15, 2024</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-400" />
                <div>
                  <p className="text-sm font-medium text-900">{t('organizations.lastLogin')}</p>
                  <p className="text-sm text-600">2 hours ago</p>
                </div>
              </div>
            </div>
          </div>

          {/* Usage Statistics */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold text-900 mb-4">
              {t('organizations.usageStats')}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">
                      {t('dashboard.activeUsers')}
                    </p>
                    <p className="text-2xl font-bold text-blue-600">
                      {organization.users}
                    </p>
                    <p className="text-xs text-blue-700">
                      of {organization.numberOfLicenses} {t('organizations.licenses')}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <CreditCard className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-green-900">
                      {t('organizations.monthlyRevenue')}
                    </p>
                    <p className="text-2xl font-bold text-green-600">
                      ${organization.monthlyRevenue.toLocaleString()}
                    </p>
                    <p className="text-xs text-green-700">
                      {t('organizations.currentBillingPeriod')}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Building2 className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="text-sm font-medium text-purple-900">
                      {t('organizations.licenseUtilization')}
                    </p>
                    <p className="text-2xl font-bold text-purple-600">
                      {Math.round(
                        (organization.users / organization.numberOfLicenses) *
                          100
                      )}
                      %
                    </p>
                    <p className="text-xs text-purple-700">{t('organizations.currentUsage')}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Billing Information */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold text-900 mb-4">
              {t('organizations.billingInformation')}
            </h3>
            <div className="bg-background-50 p-4 rounded-lg">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-900">{t('organizations.billingCycle')}</p>
                  <p className="text-sm text-600">Monthly</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-900">
                    {t('organizations.nextBillingDate')}
                  </p>
                  <p className="text-sm text-600">February 15, 2024</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-900">{t('organizations.paymentMethod')}</p>
                  <p className="text-sm text-600">**** **** **** 1234</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-900">{t('organizations.totalLicenses')}</p>
                  <p className="text-sm text-600">
                    {organization.numberOfLicenses} {t('organizations.users')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default OrganizationDetailsModal;
