import { useEffect, useRef, useState } from "react";
import io from "socket.io-client";
import { useAuth } from "@/hooks/useAuth";
import { useDecodedJwt } from "@/hooks/useDecodedJwt";

// Environment variables for Socket.IO configuration
const SOCKET_URL = import.meta.env.VITE_SOCKET || "http://localhost:8081";

// Socket connection options
const SOCKET_OPTIONS = {
  autoConnect: false,
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  upgrade: true,
  randomizationFactor: 0.5,
  transports: ["websocket", "polling"] as const,
};

// Custom hook for Socket.IO connection management
export const useSocket = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const socketRef = useRef<Socket | null>(null);
  const { isAuthenticated, accessToken } = useAuth();
  const jwtPayload = useDecodedJwt();

  useEffect(() => {
    // Only connect if user is authenticated and we have necessary data
    if (!isAuthenticated || !accessToken || !jwtPayload) {
      return;
    }

    // Create socket connection
    const socket = io(SOCKET_URL, {
      ...SOCKET_OPTIONS,
      auth: {
        token: accessToken,
      },
    });

    socketRef.current = socket;

    // Connection event handlers
    socket.on("connect", () => {
      console.log("Socket connected:", socket.id);
      setIsConnected(true);
      setConnectionError(null);

      // Join user-specific room after connection with unique socket ID format
      const userId = jwtPayload.sub || jwtPayload.userId || jwtPayload.id;
      const uniqSocketID = jwtPayload.uniqSocketID || jwtPayload.uniqSocketId || userId;

      if (userId && uniqSocketID) {
        const uniqSocketId_userId = `${uniqSocketID}_${userId}`;
        socket.emit("join", { user: uniqSocketId_userId });
        console.log("Joined socket room with uniqSocketId_userId:", uniqSocketId_userId);
      }
    });

    socket.on("disconnect", (reason: string) => {
      console.log("Socket disconnected:", reason);
      setIsConnected(false);
    });

    socket.on("connect_error", (error: Error) => {
      console.error("Socket connection error:", error);
      setConnectionError(error.message);
      setIsConnected(false);
    });

    socket.on("reconnect", (attemptNumber: number) => {
      console.log("Socket reconnected after", attemptNumber, "attempts");
      setIsConnected(true);
      setConnectionError(null);
    });

    socket.on("reconnect_error", (error: Error) => {
      console.error("Socket reconnection error:", error);
      setConnectionError(error.message);
    });

    // Connect the socket
    socket.connect();

    // Cleanup function
    return () => {
      console.log("Cleaning up socket connection");
      socket.disconnect();
      socketRef.current = null;
      setIsConnected(false);
      setConnectionError(null);
    };
  }, [isAuthenticated, accessToken, jwtPayload]);

  // Helper functions
  const emit = (event: string, data?: any) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn("Socket not connected. Cannot emit event:", event);
    }
  };

  const on = (event: string, callback: (...args: any[]) => void) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback);
    }
  };

  const off = (event: string, callback?: (...args: any[]) => void) => {
    if (socketRef.current) {
      socketRef.current.off(event, callback);
    }
  };

  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
  };

  const reconnect = () => {
    if (socketRef.current) {
      socketRef.current.connect();
    }
  };

  return {
    socket: socketRef.current,
    isConnected,
    connectionError,
    emit,
    on,
    off,
    disconnect,
    reconnect,
  };
};

// Export default for backward compatibility
export default useSocket;
