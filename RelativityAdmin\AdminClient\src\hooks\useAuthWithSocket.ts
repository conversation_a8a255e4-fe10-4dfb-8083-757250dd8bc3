import { useAuth } from "@/hooks/useAuth";
import { useSocket } from "@/hooks/useSocket";

/**
 * Custom hook that combines authentication and socket functionality
 * Provides an integrated logout that handles both socket and auth logout
 */
export const useAuthWithSocket = () => {
  const auth = useAuth();
  const socket = useSocket();

  // Integrated logout function that uses socket logout first, then auth logout
  const logout = async () => {
    try {
      // First, perform socket logout if connected
      if (socket.isConnected && socket.logout) {
        console.log("🔌 Performing socket logout...");
        await socket.logout();
      }
    } catch (error) {
      console.error("❌ Socket logout failed:", error);
      // Continue with regular logout even if socket logout fails
    }

    // Then perform regular auth logout (without socket logout callback)
    try {
      await auth.logout();
    } catch (error) {
      console.error("❌ Auth logout failed:", error);
      throw error;
    }
  };

  return {
    ...auth,
    ...socket,
    logout, // Override logout with integrated version
  };
};

export default useAuthWithSocket;
