
import { Shield, Key, Smartphone, Link, Users } from 'lucide-react';
import { SecurityFeature, AuthStat } from './types';

export const securityFeatures: SecurityFeature[] = [
  {
    title: 'Single Sign-On (SSO)',
    description: 'Centralized authentication across all organizations',
    status: 'Ready for Integration',
    icon: Link,
    color: 'text-blue-600',
  },
  {
    title: '2-Factor Authentication',
    description: 'SMS, Email, and Authenticator app support',
    status: 'Ready for Integration',
    icon: Smartphone,
    color: 'text-green-600',
  },
  {
    title: 'Blockchain Authentication',
    description: 'Optional blockchain-based identity verification',
    status: 'Ready for Integration',
    icon: Key,
    color: 'text-purple-600',
  },
  {
    title: 'Role-Based Access',
    description: 'Granular permission system for organizations',
    status: 'Active',
    icon: Users,
    color: 'text-orange-600',
  },
];

export const authStats: AuthStat[] = [
  { label: 'Total Users with 2FA', value: '1,089', percentage: 87 },
  { label: 'SSO Enabled Orgs', value: '18', percentage: 75 },
  { label: 'Blockchain Auth Users', value: '234', percentage: 19 },
  { label: 'Active Sessions', value: '892', percentage: 71 },
];
