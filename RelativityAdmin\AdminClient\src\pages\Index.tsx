import React, { useEffect, useState } from "react";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/Sidebar";
import DashboardOverview from "@/components/dashboard/DashboardOverview";
import OrganizationManagement from "@/components/organizations/OrganizationManagement";
import PersonnelManagement from "@/components/personnel/PersonnelManagement";
import LicenseManagement from "@/components/licenses/LicenseManagement";
import UserTokenManagement from "@/components/tokens/UserTokenManagement";
import SecurityManagement from "@/components/security/SecurityManagement";
import RolesAndPermissions from "@/components/RolesPermission/RolesAndPermissions";
import SocketIo from 'socket.io-client';
import { useLanguage } from "@relativity/shared/dist/i18n/LanguageProvider";
import { useDecodedJwt } from "@relativity/shared";

const Index = () => {
  const [activeSection, setActiveSection] = useState("dashboard");
  const { t } = useLanguage();

  const jwtTokenPayload = useDecodedJwt()

  const socket = SocketIo(process.env.VITE_SOCKET, {
    autoConnect: false,
    reconnection: false,
    upgrade: true,
    randomizationFactor: 0.5,
    transports: ["websocket", "polling"]
  });



  socket.connect();
  socket.emit('join', { user: userId, source: source });


  useEffect(() => {

  }, [socket]);

  const renderActiveSection = () => {
    switch (activeSection) {
      case "dashboard":
        return <DashboardOverview />;
      case "organizations":
        return <OrganizationManagement />;
      case "personnel":
        return <PersonnelManagement />;
      case "roles":
        return <RolesAndPermissions />;
      case "licenses":
        return <LicenseManagement />;
      case "tokens":
        return <UserTokenManagement />;
      case "security":
        return <SecurityManagement />;
      case "settings":
        return (
          <div className="space-y-6">
            <h2 className="text-3xl font-bold text-foreground">
              {t("nav.settings")}
            </h2>
            <p className="text-muted-foreground">
              System configuration and preferences
            </p>
          </div>
        );
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex h-[calc(100vh-73px)]">
        <Sidebar
          activeSection={activeSection}
          onSectionChange={setActiveSection}
        />
        <main className="flex-1 overflow-auto p-6">
          {renderActiveSection()}
        </main>
      </div>
    </div>
  );
};

export default Index;
