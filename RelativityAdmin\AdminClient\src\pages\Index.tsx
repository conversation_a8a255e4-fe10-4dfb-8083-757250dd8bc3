import React, { useEffect, useState } from "react";
import Header from "@/components/layout/header";
import Sidebar from "@/components/layout/Sidebar";
import DashboardOverview from "@/components/dashboard/DashboardOverview";
import OrganizationManagement from "@/components/organizations/OrganizationManagement";
import PersonnelManagement from "@/components/personnel/PersonnelManagement";
import LicenseManagement from "@/components/licenses/LicenseManagement";
import UserTokenManagement from "@/components/tokens/UserTokenManagement";
import SecurityManagement from "@/components/security/SecurityManagement";
import RolesAndPermissions from "@/components/RolesPermission/RolesAndPermissions";
import { useAuthWithSocket } from "@/hooks/useAuthWithSocket";
import { useLanguage } from "@relativity/shared/dist/i18n/LanguageProvider";

const Index = () => {
  const [activeSection, setActiveSection] = useState("dashboard");
  const { t } = useLanguage();

  // Initialize integrated auth and socket connection
  const { socket, isConnected, connectionError, on, off, logout } = useAuthWithSocket();

  useEffect(() => {
    // Set up any additional socket event listeners here
    if (socket && isConnected) {
      // Example: Listen for notifications
      const handleNotification = (data: any) => {
        console.log("Received notification:", data);
        // Handle notification logic here
      };

      on("notification", handleNotification);

      // Cleanup listeners on unmount
      return () => {
        off("notification", handleNotification);
      };
    }
  }, [socket, isConnected, on, off]);

  // Log connection status for debugging
  useEffect(() => {
    if (connectionError) {
      console.error("Socket connection error:", connectionError);
    }
  }, [connectionError]);

  // The logout function from useAuthWithSocket already integrates socket and auth logout
  // You can now use the 'logout' function anywhere in your app for integrated logout

  const renderActiveSection = () => {
    switch (activeSection) {
      case "dashboard":
        return <DashboardOverview />;
      case "organizations":
        return <OrganizationManagement />;
      case "personnel":
        return <PersonnelManagement />;
      case "roles":
        return <RolesAndPermissions />;
      case "licenses":
        return <LicenseManagement />;
      case "tokens":
        return <UserTokenManagement />;
      case "security":
        return <SecurityManagement />;
      case "settings":
        return (
          <div className="space-y-6">
            <h2 className="text-3xl font-bold text-foreground">
              {t("nav.settings")}
            </h2>
            <p className="text-muted-foreground">
              System configuration and preferences
            </p>
          </div>
        );
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex h-[calc(100vh-73px)]">
        <Sidebar
          activeSection={activeSection}
          onSectionChange={setActiveSection}
        />
        <main className="flex-1 overflow-auto p-6">
          {renderActiveSection()}
        </main>
      </div>
    </div>
  );
};

export default Index;
