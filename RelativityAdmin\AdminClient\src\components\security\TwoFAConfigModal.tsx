
import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

interface TwoFAConfigModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const TwoFAConfigModal: React.FC<TwoFAConfigModalProps> = ({ open, onOpenChange }) => {
  const { toast } = useToast();
  const [settings, setSettings] = useState({
    smsEnabled: true,
    emailEnabled: true,
    appEnabled: true,
    enforceForAdmins: false,
    sessionTimeout: 24
  });

  const handleSave = () => {
    toast({
      title: "2FA Settings Saved",
      description: "Two-factor authentication settings have been updated successfully.",
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Two-Factor Authentication Settings</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Authentication Methods</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="sms">SMS Authentication</Label>
                <Switch
                  id="sms"
                  checked={settings.smsEnabled}
                  onCheckedChange={(checked) => setSettings({ ...settings, smsEnabled: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="email">Email Authentication</Label>
                <Switch
                  id="email"
                  checked={settings.emailEnabled}
                  onCheckedChange={(checked) => setSettings({ ...settings, emailEnabled: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="app">Authenticator Apps</Label>
                <Switch
                  id="app"
                  checked={settings.appEnabled}
                  onCheckedChange={(checked) => setSettings({ ...settings, appEnabled: checked })}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Security Policies</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="enforce">Enforce for Administrators</Label>
                <Switch
                  id="enforce"
                  checked={settings.enforceForAdmins}
                  onCheckedChange={(checked) => setSettings({ ...settings, enforceForAdmins: checked })}
                />
              </div>
              <div>
                <Label htmlFor="timeout">Session Timeout (hours)</Label>
                <Input
                  id="timeout"
                  type="number"
                  value={settings.sessionTimeout}
                  onChange={(e) => setSettings({ ...settings, sessionTimeout: parseInt(e.target.value) || 24 })}
                  min="1"
                  max="168"
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Settings
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TwoFAConfigModal;
