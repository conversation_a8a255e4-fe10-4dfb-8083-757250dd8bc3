import { useAuth } from "@/hooks/useAuth";

// Helper to decode JWT and extract payload
function decodeJwtPayload(token: string | null): any {
  if (!token) return null;
  try {
    const payload = token.split(".")[1];
    const decoded = atob(payload.replace(/-/g, "+").replace(/_/g, "/"));
    return JSON.parse(decoded);
  } catch {
    return null;
  }
}

export function useDecodedJwt() {
  const { accessToken } = useAuth();
  return decodeJwtPayload(accessToken);
}
