import React, { useState } from "react";
import { PersonnelManagementInfo } from "@relativity/shared"
import { ApplicationName } from "@/lib/Schemas/Personnel";

// Wrapper component to handle appName and selectedOrganizationId props
const PersonnelManagementInfoWrapper = ({ appName, selectedOrganizationId, apiUrl, orgApiUrl }: { appName: string, selectedOrganizationId: string, apiUrl: string, orgApiUrl: string }) => {
  // Pass the props to the PersonnelManagementInfo component
  // Note: We're casting to any to bypass TypeScript restrictions since the shared component
  // may not have proper type definitions for these props
  const PersonnelManagementInfoWithProps = PersonnelManagementInfo as any;

  return (
    <PersonnelManagementInfoWithProps
      appName={appName}
      selectedOrganizationId={selectedOrganizationId}
      apiUrl={apiUrl}
      orgApiUrl={orgApiUrl}
    />
  );
}

const PersonnelManagement: React.FC = () => {
  const [selectedOrg, setSelectedOrg] = useState("all");
  return (
    <div className="space-y-6">
      <PersonnelManagementInfoWrapper
        appName={ApplicationName.RELATIVITY_ADMIN}
        selectedOrganizationId={selectedOrg}
        apiUrl={import.meta.env.VITE_ADMIN_API_BASE_URL}
        orgApiUrl={import.meta.env.VITE_ORG_API_BASE_URL}
      />
    </div>
  );
};

export default PersonnelManagement;
