
import * as z from 'zod';

export const addLicenseSchema = z.object({
  applicationName: z.string().min(1, 'Application name is required'),
  cycleLength: z.enum(['monthly', 'quarterly', 'annual']),
  pricePerUser: z.number().min(1, 'Price per user must be greater than 0'),
  minimumUsers: z.number().min(1, 'Minimum users must be at least 1'),
  description: z.string().optional(),
});

export type AddLicenseFormData = z.infer<typeof addLicenseSchema>;
