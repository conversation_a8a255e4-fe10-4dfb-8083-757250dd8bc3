import { useEffect } from "react";
import { useLocation } from "react-router-dom";

const NotFound = () => {
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const refreshToken = params.get("refreshToken");

  useEffect(() => {
    if (refreshToken) {
      // Clean URL without full page reload
      const url = new URL(window.location.href);
      url.searchParams.delete("refreshToken");
      window.history.replaceState(
        {},
        document.title,
        url.pathname + url.search
      );
    }
  }, [refreshToken]);

  const redirectToHome = () => {
    window.location.href = `${
      import.meta.env.VITE_DASHBOARD_URL
    }?refreshToken=${refreshToken}`;
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">403 Forbidden</h1>
        <p className="text-xl text-600 mb-4">
          You do not have access to this application. Please contact your
          administrator.
        </p>
        <a
          onClick={() => redirectToHome()}
          className="text-blue-500 hover:text-blue-700 underline">
          Return to Home
        </a>
      </div>
    </div>
  );
};

export default NotFound;
