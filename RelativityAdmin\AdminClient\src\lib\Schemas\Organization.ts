import * as z from "zod";
import { OTPCodeType } from "./Master";

// Define the Organization type based on your form schema
export interface Organization {
  _id?: string;
  organizationName: string;
  domain: string;
  numberOfLicenses: number;
  address: {
    streetAddress: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  users?: number;
  status?: OrganizationStatus;
  monthlyRevenue?: number;
  description?: string;
  is2FAEnabled?: boolean;
  MFAType?: OTPCodeType[];
  selectedAppLicenses?: any[];
}

export enum OrganizationStatus {
  Active = "Active",
  Pending = "Pending",
  Suspended = "Suspended",
}

export interface OrganizationManageModalProps {
  organization: Organization | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (updatedOrg: Organization) => void;
  licensePackages: any[];
}

export type CreateOrganizationData = Omit<Organization, "_id">;
export type UpdateOrganizationData = Partial<CreateOrganizationData> & {
  _id: string;
};

export interface OrganizationDetailsModalProps {
  organization: Organization | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const organizationSchema = z.object({
  organizationName: z.string().min(1, "Organization name is required"),
  domain: z.string().min(1, "Domain is required"),
  numberOfLicenses: z.number().min(1, "At least 1 license is required"),
  address: z.object({
    streetAddress: z.string().min(1, "Street address is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    zipCode: z.string().min(1, "ZIP code is required"),
    country: z.string().min(1, "Country is required"),
  }),
  description: z.string().optional(),
  status: z
    .nativeEnum(OrganizationStatus)
    .optional()
    .default(OrganizationStatus.Active),
  users: z.number().optional(),
  monthlyRevenue: z.number().optional(),
  is2FAEnabled: z.boolean().optional().default(false),
  MFAType: z.array(z.nativeEnum(OTPCodeType)).optional(),
});

export type OrganizationFormData = z.infer<typeof organizationSchema>;
