import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@/lib/axiosInterceptor";

// Types
export interface DashboardData {
  overview: {
    totalOrganizations: {
      count: number;
      growth: string;
    };
    activeUsers: {
      count: number;
      growth: string;
    };
    licenseRevenue: {
      amount: number;
      formatted: string;
      growth: string;
    };
    growthRate: {
      percentage: string;
      description: string;
    };
  };
  recentOrganizations: Array<{
    name: string;
    users: string;
    status: string;
  }>;
  licenseUsage: {
    totalLicensesSold: number;
    activeUsers: number;
    utilizationRate: {
      percentage: string;
      decimal: number;
    };
  };
}

// Environment variables for API configuration
const API_BASE_URL = import.meta.env.VITE_ADMIN_API_BASE_URL || "http://localhost:5007";

// API functions
const dashboardApi = {
  // Get complete dashboard data
  getDashboardData: async (): Promise<DashboardData> => {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/api/dashboard/complete`);
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to fetch dashboard data");
    }
  },

  // Get dashboard overview only
  getDashboardOverview: async (): Promise<DashboardData['overview']> => {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/api/dashboard/overview`);
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to fetch dashboard overview");
    }
  },

  // Get recent organizations
  getRecentOrganizations: async (): Promise<DashboardData['recentOrganizations']> => {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/api/dashboard/recent-organizations`);
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to fetch recent organizations");
    }
  },

  // Get license usage data
  getLicenseUsage: async (): Promise<DashboardData['licenseUsage']> => {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/api/dashboard/license-usage`);
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to fetch license usage data");
    }
  },
};

// Query Keys
const dashboardKeys = {
  all: ['dashboard'] as const,
  complete: () => [...dashboardKeys.all, 'complete'] as const,
  overview: () => [...dashboardKeys.all, 'overview'] as const,
  recentOrganizations: () => [...dashboardKeys.all, 'recent-organizations'] as const,
  licenseUsage: () => [...dashboardKeys.all, 'license-usage'] as const,
};

// ----------- Queries -----------

// Get complete dashboard data
export const useDashboardData = () => {
  return useQuery({
    queryKey: dashboardKeys.complete(),
    queryFn: dashboardApi.getDashboardData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // 10 minutes
  });
};

// Get dashboard overview only
export const useDashboardOverview = () => {
  return useQuery({
    queryKey: dashboardKeys.overview(),
    queryFn: dashboardApi.getDashboardOverview,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get recent organizations
export const useRecentOrganizations = () => {
  return useQuery({
    queryKey: dashboardKeys.recentOrganizations(),
    queryFn: dashboardApi.getRecentOrganizations,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get license usage data
export const useLicenseUsage = () => {
  return useQuery({
    queryKey: dashboardKeys.licenseUsage(),
    queryFn: dashboardApi.getLicenseUsage,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Default export for backward compatibility
export default useDashboardData;