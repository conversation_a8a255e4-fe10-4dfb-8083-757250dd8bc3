
import React, { useState } from 'react';
import SSOConfigModal from './SSOConfigModal';
import TwoFAConfigModal from './TwoFAConfigModal';
import BlockchainConfigModal from './BlockchainConfigModal';
import SecurityFeaturesGrid from './SecurityFeaturesGrid';
import AuthenticationStatistics from './AuthenticationStatistics';
import SSOConfigurationSection from './SSOConfigurationSection';
import AuthenticationMethodsSection from './AuthenticationMethodsSection';

const SecurityManagement: React.FC = () => {
  const [ssoModalOpen, setSsoModalOpen] = useState(false);
  const [ssoModalType, setSsoModalType] = useState<'saml' | 'oauth' | 'openid'>('saml');
  const [twoFAModalOpen, setTwoFAModalOpen] = useState(false);
  const [blockchainModalOpen, setBlockchainModalOpen] = useState(false);

  const handleSSOConfigure = (type: 'saml' | 'oauth' | 'openid') => {
    setSsoModalType(type);
    setSsoModalOpen(true);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold text-gray-900">Security & Authentication</h2>
        <p className="text-gray-600 mt-2">Manage authentication systems and security features</p>
      </div>

      <SecurityFeaturesGrid
        onTwoFAOpen={() => setTwoFAModalOpen(true)}
        onBlockchainOpen={() => setBlockchainModalOpen(true)}
        onSSOOpen={() => setSsoModalOpen(true)}
      />

      <AuthenticationStatistics />

      <SSOConfigurationSection onSSOConfigure={handleSSOConfigure} />

      <AuthenticationMethodsSection
        onTwoFAOpen={() => setTwoFAModalOpen(true)}
        onBlockchainOpen={() => setBlockchainModalOpen(true)}
      />

      <SSOConfigModal 
        open={ssoModalOpen} 
        onOpenChange={setSsoModalOpen}
        type={ssoModalType}
      />
      <TwoFAConfigModal 
        open={twoFAModalOpen} 
        onOpenChange={setTwoFAModalOpen}
      />
      <BlockchainConfigModal 
        open={blockchainModalOpen} 
        onOpenChange={setBlockchainModalOpen}
      />
    </div>
  );
};

export default SecurityManagement;
