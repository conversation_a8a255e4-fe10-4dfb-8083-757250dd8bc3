import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { LicensePackage } from './types';
import { addLicenseSchema, AddLicenseFormData } from './schemas';
import LicenseFormFields from './LicenseFormFields';
import PricingPreview from './PricingPreview';

interface AddLicensePackageModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddLicense: (license: LicensePackage) => Promise<void>;
  applications: { id: string, value: string; label: string; description: string }[];
  license?: LicensePackage | null;
  isSubmitting?: boolean;
}

const AddLicensePackageModal: React.FC<AddLicensePackageModalProps> = ({
  open,
  onOpenChange,
  onAddLicense,
  applications,
  license = null,
  isSubmitting = false,
}) => {
  const isEditMode = Boolean(license);

  const form = useForm<AddLicenseFormData>({
    resolver: zodResolver(addLicenseSchema),
    defaultValues: {
      applicationName: '',
      cycleLength: 'monthly',
      pricePerUser: 75,
      minimumUsers: 5,
      description: '',
    },
  });

  // Reset form when modal opens/closes or when license changes
  useEffect(() => {
    if (open) {
      if (isEditMode && license) {
        // For edit mode, populate form with existing license data
        const existingPackage = license.package?.[0];
        form.reset({
          applicationName: license.applicationName,
          cycleLength: existingPackage?.cycleLength || 'monthly',
          pricePerUser: existingPackage?.pricePerUser || 75,
          minimumUsers: existingPackage?.minimumUsers || 5,
          description: existingPackage?.description || '',
        });
      } else {
        // For add mode, reset to default values
        form.reset({
          applicationName: '',
          cycleLength: 'monthly',
          pricePerUser: 75,
          minimumUsers: 5,
          description: '',
        });
      }
    }
  }, [open, license, isEditMode, form]);

  const onSubmit = async (data: AddLicenseFormData) => {
    try {
      if (isEditMode && license) {
        // Edit mode: Update existing license
        const updatedLicense: LicensePackage = {
          ...license,
          applicationName: data.applicationName,
          package: [
            {
              cycleLength: data.cycleLength,
              pricePerUser: data.pricePerUser,
              minimumUsers: data.minimumUsers,
              description: data.description,
              status: 'Active',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            // Keep other existing packages that don't match the current cycle
            // ...license.package.filter(pkg => pkg.cycleLength !== data.cycleLength)
          ],
          updatedAt: new Date().toISOString(),
        };
        await onAddLicense(updatedLicense);
      } else {
        // Add mode: Create new license
        const newLicense: LicensePackage = {
          id: Date.now().toString(), // This will be ignored by the API and replaced with proper ID
          applicationName: data.applicationName,
          description: data.description,
          package: [
            {
              cycleLength: data.cycleLength,
              pricePerUser: data.pricePerUser,
              minimumUsers: data.minimumUsers,
              description: data.description,
              status: 'Active',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            }
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        await onAddLicense(newLicense);
      }

      form.reset();
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Form submission error:', error);
    }
  };

  const watchedCycle = form.watch('cycleLength');
  const watchedPrice = form.watch('pricePerUser');
  const watchedMinUsers = form.watch('minimumUsers');

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? 'Edit License Package' : 'Create New License Package'}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <LicenseFormFields
              control={form.control}
              applications={applications}
            // isEditMode={isEditMode}
            />

            <PricingPreview
              cycleLength={watchedCycle}
              pricePerUser={watchedPrice}
              minimumUsers={watchedMinUsers}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="min-w-[140px]"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isEditMode ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  isEditMode ? 'Update License Package' : 'Create License Package'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddLicensePackageModal;