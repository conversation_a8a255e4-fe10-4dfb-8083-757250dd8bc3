
export interface LicensePackage {
  _id?: string;
  id: string;
  applicationName: string;
  package: CycleDetails[];
  createdAt: string;
  updatedAt: string;
  description: string,
}
export interface CycleDetails {
  cycleLength: 'monthly' | 'quarterly' | 'annual';
  pricePerUser: number;
  minimumUsers: number;
  description?: string;
  status: 'Active' | 'Inactive';
  createdAt: string;
  updatedAt: string;
}

export interface GroupedLicense {
  description?: string;
  createdDate: string;
  status: string;
  packages: Record<string, LicensePackage>;
}

export interface LicenseManagementProps {
  licensePackages: LicensePackage[];
  onEditClick: (packageData: LicensePackage) => void;
}
