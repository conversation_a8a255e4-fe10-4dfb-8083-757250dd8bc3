
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Building2, Users, CreditCard } from 'lucide-react';
import { UserToken, TokenIssuanceRequest } from './types';

interface IssueTokenModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onIssueToken: (token: UserToken) => void;
}

// Mock data for organizations and their license pools
const organizationsWithLicenses = [
  {
    id: '1',
    name: 'TechCorp Solutions',
    availableLicenses: 5,
    licensePackages: [
      { id: '1', applicationName: 'ClientHub Pro', cycleLength: 'monthly', availableTokens: 5 },
      { id: '3', applicationName: 'ClientHub Pro', cycleLength: 'quarterly', availableTokens: 3 },
      { id: '4', applicationName: 'ClientHub Pro', cycleLength: 'annual', availableTokens: 2 },
    ]
  },
  {
    id: '2',
    name: 'Global Industries',
    availableLicenses: 3,
    licensePackages: [
      { id: '2', applicationName: 'Analytics Suite', cycleLength: 'quarterly', availableTokens: 3 },
    ]
  },
];

const IssueTokenModal: React.FC<IssueTokenModalProps> = ({
  open,
  onOpenChange,
  onIssueToken,
}) => {
  const [selectedOrgId, setSelectedOrgId] = useState('');
  const [selectedLicenseId, setSelectedLicenseId] = useState('');
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');

  const selectedOrg = organizationsWithLicenses.find(org => org.id === selectedOrgId);
  const selectedLicense = selectedOrg?.licensePackages.find(pkg => pkg.id === selectedLicenseId);

  const calculateExpiryDate = (cycleLength: string) => {
    const now = new Date();
    switch (cycleLength) {
      case 'monthly':
        return new Date(now.setMonth(now.getMonth() + 1));
      case 'quarterly':
        return new Date(now.setMonth(now.getMonth() + 3));
      case 'annual':
        return new Date(now.setFullYear(now.getFullYear() + 1));
      default:
        return new Date(now.setMonth(now.getMonth() + 1));
    }
  };

  const handleSubmit = () => {
    if (!selectedOrg || !selectedLicense || !userName || !userEmail) {
      return;
    }

    const newToken: UserToken = {
      id: Date.now().toString(),
      userId: Date.now().toString(),
      userName,
      userEmail,
      organizationId: selectedOrg.id,
      organizationName: selectedOrg.name,
      licensePackageId: selectedLicense.id,
      applicationName: selectedLicense.applicationName,
      cycleLength: selectedLicense.cycleLength as 'monthly' | 'quarterly' | 'annual',
      issuedDate: new Date().toISOString().split('T')[0],
      expiryDate: calculateExpiryDate(selectedLicense.cycleLength).toISOString().split('T')[0],
      status: 'Active',
      issuedBy: 'Admin',
    };

    onIssueToken(newToken);
    
    // Reset form
    setSelectedOrgId('');
    setSelectedLicenseId('');
    setUserName('');
    setUserEmail('');
    onOpenChange(false);
  };

  const getCycleColor = (cycle: string) => {
    switch (cycle) {
      case 'monthly': return 'bg-blue-100 text-blue-800';
      case 'quarterly': return 'bg-purple-100 text-purple-800';
      case 'annual': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5 text-blue-600" />
            <span>Issue User Token</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Organization Selection */}
          <div className="space-y-2">
            <Label htmlFor="organization">Select Organization</Label>
            <Select value={selectedOrgId} onValueChange={setSelectedOrgId}>
              <SelectTrigger>
                <SelectValue placeholder="Choose an organization" />
              </SelectTrigger>
              <SelectContent>
                {organizationsWithLicenses.map((org) => (
                  <SelectItem key={org.id} value={org.id}>
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-4 w-4" />
                      <span>{org.name}</span>
                      <Badge variant="outline">{org.availableLicenses} available</Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* License Package Selection */}
          {selectedOrg && (
            <div className="space-y-2">
              <Label htmlFor="license">Select License Package</Label>
              <Select value={selectedLicenseId} onValueChange={setSelectedLicenseId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a license package" />
                </SelectTrigger>
                <SelectContent>
                  {selectedOrg.licensePackages.map((pkg) => (
                    <SelectItem key={pkg.id} value={pkg.id}>
                      <div className="flex items-center space-x-2">
                        <span>{pkg.applicationName}</span>
                        <Badge className={getCycleColor(pkg.cycleLength)}>
                          {pkg.cycleLength}
                        </Badge>
                        <Badge variant="outline">{pkg.availableTokens} tokens</Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* User Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="userName">User Name</Label>
              <Input
                id="userName"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                placeholder="Enter user's full name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="userEmail">User Email</Label>
              <Input
                id="userEmail"
                type="email"
                value={userEmail}
                onChange={(e) => setUserEmail(e.target.value)}
                placeholder="Enter user's email"
              />
            </div>
          </div>

          {/* Token Preview */}
          {selectedLicense && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Token Preview</h4>
              <div className="space-y-1 text-sm">
                <p><span className="font-medium">Application:</span> {selectedLicense.applicationName}</p>
                <p><span className="font-medium">Billing Cycle:</span> {selectedLicense.cycleLength}</p>
                <p><span className="font-medium">Valid Until:</span> {calculateExpiryDate(selectedLicense.cycleLength).toLocaleDateString()}</p>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={!selectedOrg || !selectedLicense || !userName || !userEmail}
          >
            Issue Token
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default IssueTokenModal;
