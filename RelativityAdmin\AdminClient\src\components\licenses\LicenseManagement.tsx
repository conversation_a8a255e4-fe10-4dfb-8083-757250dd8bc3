import React, { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { toast } from "sonner";
import AddLicensePackageModal from "./AddLicensePackageModal";
import LicenseSummaryCards from "./LicenseSummaryCards";
import LicenseApplicationCard from "./LicenseApplicationCard";
import { LicensePackage } from "./types";
import { useGetApplications } from "@/hooks/useMasters";
import {
  useLicenses,
  useCreateLicense,
  useUpdateLicense,
} from "@/hooks/useLicensePackage";

const LicenseManagement: React.FC = () => {
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [selectedLicense, setSelectedLicense] = useState<LicensePackage | null>(null);

  const {
    data: licensePackages = [],
    isLoading: licensesLoading,
    error: licensesError,
  } = useLicenses();

  const {
    data: applications = [],
    isLoading: applicationsLoading,
  } = useGetApplications();

  const createLicenseMutation = useCreateLicense();
  const updateLicenseMutation = useUpdateLicense();

  const handleAddLicense = async (newLicense: LicensePackage) => {
    try {
      const { id, ...licenseData } = newLicense;
      await createLicenseMutation.mutateAsync(licenseData);
      toast.success("License package created successfully!");
      closeModal();
    } catch (error) {
      const message = error instanceof Error ? error.message : "An unexpected error occurred";
      toast.error(message);
      console.error("Create license error:", error);
    }
  };

  const handleEditLicense = async (updatedLicense: LicensePackage) => {
    try {
      if (!updatedLicense.id) throw new Error("License ID is required for updates");
      await updateLicenseMutation.mutateAsync({ id: updatedLicense.id, data: updatedLicense });
      toast.success("License package updated successfully!");
      closeModal();
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to update license package";
      toast.error(message);
      console.error("Update license error:", error);
    }
  };

  const openModalForEdit = (license: LicensePackage) => {
    setSelectedLicense(license);
    setAddModalOpen(true);
  };

  const openModalForAdd = () => {
    setSelectedLicense(null);
    setAddModalOpen(true);
  };

  const closeModal = () => {
    setAddModalOpen(false);
    setSelectedLicense(null);
  };

  const groupedLicenses = useMemo(
    () =>
      licensePackages.reduce((acc, license) => {
        acc[license.applicationName] = {
          id: license._id,
          applicationName: license.applicationName,
          description: license.description,
          package: license.package,
          createdAt: license.createdAt,
          updatedAt: license.updatedAt,
        };
        return acc;
      }, {} as Record<string, LicensePackage>),
    [licensePackages]
  );

  // Loading
  if (licensesLoading || applicationsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading...</div>
      </div>
    );
  }

  // Error
  if (licensesError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-600">
          Error loading licenses: {licensesError.message}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold text-gray-900">License Management</h2>
            <p className="text-gray-600 mt-2">Manage license packages and configurations</p>
          </div>
          <Button
            className="bg-blue-600 hover:bg-blue-700"
            onClick={openModalForAdd}
            disabled={createLicenseMutation.isPending}
          >
            <Plus className="mr-2 h-4 w-4" />
            {createLicenseMutation.isPending ? "Creating..." : "Create License Package"}
          </Button>
        </div>

        <LicenseSummaryCards
          licensePackages={licensePackages}
          groupedLicenses={groupedLicenses}
        />

        <div className="space-y-6">
          {Object.entries(groupedLicenses).map(([applicationName, appData]) => (
            <LicenseApplicationCard
              key={applicationName}
              applicationName={applicationName}
              appData={appData}
              onEditClick={openModalForEdit}
            />
          ))}
        </div>

        {licensePackages.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg">No license packages found</div>
            <p className="text-gray-400 mt-2">Create your first license package to get started</p>
          </div>
        )}
      </div>

      <AddLicensePackageModal
        open={addModalOpen}
        onOpenChange={(open) => (open ? setAddModalOpen(true) : closeModal())}
        onAddLicense={selectedLicense ? handleEditLicense : handleAddLicense}
        license={selectedLicense}
        applications={applications}
        isSubmitting={createLicenseMutation.isPending || updateLicenseMutation.isPending}
      />
    </>
  );
};

export default LicenseManagement;
