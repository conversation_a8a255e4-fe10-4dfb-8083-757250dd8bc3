
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Search, User, Building2, CreditCard, Calendar } from 'lucide-react';
import { UserToken } from './types';
import IssueTokenModal from './IssueTokenModal';

const UserTokenManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrg, setSelectedOrg] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [issueModalOpen, setIssueModalOpen] = useState(false);
  const [userTokens, setUserTokens] = useState<UserToken[]>([
    {
      id: '1',
      userId: 'user1',
      userName: '<PERSON>',
      userEmail: '<EMAIL>',
      organizationId: '1',
      organizationName: 'TechCorp Solutions',
      licensePackageId: '1',
      applicationName: 'ClientHub Pro',
      cycleLength: 'monthly',
      issuedDate: '2024-01-15',
      expiryDate: '2024-02-15',
      status: 'Active',
      issuedBy: 'Admin',
    },
    {
      id: '2',
      userId: 'user2',
      userName: 'Sarah Johnson',
      userEmail: '<EMAIL>',
      organizationId: '1',
      organizationName: 'TechCorp Solutions',
      licensePackageId: '2',
      applicationName: 'Analytics Suite',
      cycleLength: 'quarterly',
      issuedDate: '2024-01-10',
      expiryDate: '2024-04-10',
      status: 'Active',
      issuedBy: 'Admin',
    },
  ]);

  const organizations = ['TechCorp Solutions', 'Global Industries', 'Innovation Labs'];

  const filteredTokens = userTokens.filter(token => {
    const matchesSearch = token.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         token.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         token.applicationName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesOrg = selectedOrg === 'all' || token.organizationName === selectedOrg;
    const matchesStatus = selectedStatus === 'all' || token.status === selectedStatus;
    return matchesSearch && matchesOrg && matchesStatus;
  });

  const handleIssueToken = (newToken: UserToken) => {
    setUserTokens(prev => [...prev, newToken]);
  };

  const handleRevokeToken = (tokenId: string) => {
    setUserTokens(prev => 
      prev.map(token => 
        token.id === tokenId ? { ...token, status: 'Revoked' as const } : token
      )
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'Expired': return 'bg-yellow-100 text-yellow-800';
      case 'Revoked': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCycleColor = (cycle: string) => {
    switch (cycle) {
      case 'monthly': return 'bg-blue-100 text-blue-800';
      case 'quarterly': return 'bg-purple-100 text-purple-800';
      case 'annual': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">User Token Management</h2>
          <p className="text-gray-600 mt-2">Issue and manage individual user tokens from license pools</p>
        </div>
        <Button 
          className="bg-blue-600 hover:bg-blue-700"
          onClick={() => setIssueModalOpen(true)}
        >
          <Plus className="mr-2 h-4 w-4" />
          Issue Token
        </Button>
      </div>

      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search users, emails, or applications..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedOrg} onValueChange={setSelectedOrg}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by organization" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Organizations</SelectItem>
                {organizations.map(org => (
                  <SelectItem key={org} value={org}>{org}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Expired">Expired</SelectItem>
                <SelectItem value="Revoked">Revoked</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredTokens.map((token) => (
              <div key={token.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="bg-blue-100 p-2 rounded-lg">
                      <User className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{token.userName}</h3>
                      <p className="text-sm text-gray-500">{token.userEmail}</p>
                      <div className="flex items-center space-x-1 mt-1">
                        <Building2 className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">{token.organizationName}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Badge className={getStatusColor(token.status)}>
                      {token.status}
                    </Badge>
                    <Badge className={getCycleColor(token.cycleLength)}>
                      {token.cycleLength}
                    </Badge>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{token.applicationName}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      Issued: {new Date(token.issuedDate).toLocaleDateString()}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      Expires: {new Date(token.expiryDate).toLocaleDateString()}
                    </span>
                  </div>
                  
                  <div className="flex space-x-2">
                    {token.status === 'Active' && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleRevokeToken(token.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        Revoke
                      </Button>
                    )}
                    <Button 
                      variant="outline" 
                      size="sm"
                    >
                      View Details
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <IssueTokenModal
        open={issueModalOpen}
        onOpenChange={setIssueModalOpen}
        onIssueToken={handleIssueToken}
      />
    </div>
  );
};

export default UserTokenManagement;
