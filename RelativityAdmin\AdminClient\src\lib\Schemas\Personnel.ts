export enum ApplicationName {
    RELATIVITY_ADMIN = 'relativityadmin',
}
export interface Person {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    organizationId: string;
    roleId: string;
    isSystemUser: boolean;
    hasLogin: boolean;
    status: 'Active' | 'Inactive' | 'Pending';
    roleName?: string;
    organizationName?: string;
    allowedApplications?: string[];
    licenseInfo: Object;
    data: Object;
}


export interface PersonnelRoles {
    id: string;
    _id: string;
    name: string;
    description: string;
    agencyId?: number;
    createdAt: Date;
}

export interface ResetPassword {
    id?: string;
    currentPassword: string;
    password: string;
}