
import { ApplicationName } from "@/lib/Schemas/Personnel";
import { RolesManagement } from "@relativity/shared";


const RolesAndPermissions = () => {
  return (
    <div className="max-w mx-auto">

      <RolesManagement
        appName={ApplicationName.RELATIVITY_ADMIN}
        adminApiUrl={import.meta.env.VITE_ADMIN_API_BASE_URL}
        orgApiUrl=""
        orgId={""}
      />
    </div>
  );
};

export default RolesAndPermissions;
