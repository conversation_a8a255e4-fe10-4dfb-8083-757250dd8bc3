// MongoDB-style document interfaces
export interface User {
  _id: string;
  username: string;
  email: string;
  password: string;
  isEmailVerified: boolean;
  phone?: string;
  mfaEnabled: boolean;
  mfaMethod?: "email" | "sms" | "authenticator";
  totpSecret?: string;
  backupCodes?: string[];
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface OTPCode {
  _id: string;
  userId: string;
  code: string;
  type: "email" | "sms" | "password_reset";
  expiresAt: Date;
  used: boolean;
  createdAt: Date;
}
