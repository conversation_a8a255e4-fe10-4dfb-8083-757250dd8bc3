import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

const editLicenseSchema = z.object({
  applicationName: z.string().min(1, "Application name is required"),
  cycleLength: z.enum(["monthly", "quarterly", "annual"]),
  pricePerUser: z.number().min(1, "Price per user must be greater than 0"),
  minimumUsers: z.number().min(1, "Minimum users must be at least 1"),
  description: z.string().optional(),
});

type EditLicenseFormData = z.infer<typeof editLicenseSchema>;

interface LicensePackage {
  id: string;
  applicationName: string;
  cycleLength: "monthly" | "quarterly" | "annual";
  pricePerUser: number;
  minimumUsers: number;
  description?: string;
  createdDate: string;
  status: "Active" | "Inactive";
}

interface EditLicensePackageModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEditLicense: (license: LicensePackage) => void;
  license: LicensePackage | null;
  applications: { id: string; value: string; label: string; description: string }[];
}

const EditLicensePackageModal: React.FC<EditLicensePackageModalProps> = ({
  open,
  onOpenChange,
  onEditLicense,
  license,
  applications,
}) => {
  const form = useForm<EditLicenseFormData>({
    resolver: zodResolver(editLicenseSchema),
    defaultValues: {
      applicationName: "",
      cycleLength: "monthly",
      pricePerUser: 75,
      minimumUsers: 5,
      description: "",
    },
  });

  React.useEffect(() => {
    if (license) {
      form.reset({
        applicationName: license.applicationName,
        cycleLength: license.cycleLength,
        pricePerUser: license.pricePerUser,
        minimumUsers: license.minimumUsers,
        description: license.description || "",
      });
    }
  }, [license, form]);

  const onSubmit = (data: EditLicenseFormData) => {
    if (!license) return;

    const updatedLicense: LicensePackage = {
      ...license,
      applicationName: data.applicationName,
      cycleLength: data.cycleLength,
      pricePerUser: data.pricePerUser,
      minimumUsers: data.minimumUsers,
      description: data.description,
    };

    onEditLicense(updatedLicense);
    onOpenChange(false);
  };

  const getCycleMultiplier = (cycle: string) => {
    switch (cycle) {
      case "quarterly":
        return 3;
      case "annual":
        return 12;
      default:
        return 1;
    }
  };

  const watchedCycle = form.watch("cycleLength");
  const watchedPrice = form.watch("pricePerUser");
  const watchedMinUsers = form.watch("minimumUsers");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit License Package</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* <FormField
                control={form.control}
                name="applicationName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Application Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., ClientHub Pro" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              /> */}

              <FormField
                control={form.control}
                name="applicationName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Application Name</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select application name" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {applications.map((app) => (
                          <SelectItem key={app.id} value={app.value}>
                            {app.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="cycleLength"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Billing Cycle</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select billing cycle" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="annual">Annual</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Pricing Configuration
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="pricePerUser"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price per User (Monthly)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="75"
                          {...field}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="minimumUsers"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum Users</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="5"
                          {...field}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">
                      ${watchedPrice || 0}
                    </div>
                    <p className="text-sm text-gray-600">per user/month</p>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-600">
                      ${(watchedPrice || 0) * getCycleMultiplier(watchedCycle)}
                    </div>
                    <p className="text-sm text-gray-600">
                      per user/{watchedCycle}
                    </p>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">
                      $
                      {(watchedMinUsers || 0) *
                        (watchedPrice || 0) *
                        getCycleMultiplier(watchedCycle)}
                    </div>
                    <p className="text-sm text-gray-600">
                      minimum {watchedCycle} package
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes about this license package"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">Save Changes</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EditLicensePackageModal;
