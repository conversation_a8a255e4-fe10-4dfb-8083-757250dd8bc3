import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog"; // Replace with your dialog UI lib
import { Save, Check, X } from "lucide-react";

interface RoleDialogProps {
  open: boolean;
  onClose: () => void;
  role: {
    id?: number;
    name: string;
    description: string;
    permissionIds: string[];
  };
  setRole: (role: any) => void;
  isEdit: boolean;
  onSave: () => void;
  availablePermissions: {
    id: string;
    name: string;
    category: string;
  }[];
}

const RoleDialog: React.FC<RoleDialogProps> = ({
  open,
  onClose,
  role,
  setRole,
  isEdit,
  onSave,
  availablePermissions,
}) => {
    
  const groupedPermissions = availablePermissions.reduce((acc, permission) => {
    if (!acc[permission.category]) acc[permission.category] = [];
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, typeof availablePermissions>);

  const handlePermissionToggle = (permissionId: string) => {
    const exists = role.permissionIds.includes(permissionId);
    const newPerms = exists
      ? role.permissionIds.filter((p) => p !== permissionId)
      : [...role.permissionIds, permissionId];
    setRole({ ...role, permissionIds: newPerms });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEdit ? "Edit Role" : "Create Role"}</DialogTitle>
        </DialogHeader>

        <div>
          <label className="text-sm font-medium">Role Name</label>
          <input
            className="w-full px-3 py-2 border rounded-lg bg-background"
            value={role.name}
            onChange={(e) => setRole({ ...role, name: e.target.value })}
            placeholder="Enter role name"
          />
        </div>

        <div>
          <label className="text-sm font-medium">Description</label>
          <input
            className="w-full px-3 py-2 border rounded-lg bg-background"
            value={role.description}
            onChange={(e) => setRole({ ...role, description: e.target.value })}
            placeholder="Enter role description"
          />
        </div>

        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-4">Permissions</h3>
          {Object.entries(groupedPermissions).map(([category, perms]) => (
            <div key={category} className="mb-4 border p-4 rounded-lg">
              <h4 className="font-semibold mb-2">{category}</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {perms?.map((perm) => (
                  <label
                    key={perm.id}
                    className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={role.permissionIds?.includes(perm.id)}
                      onChange={() => handlePermissionToggle(perm.id)}
                    />
                    <span>{perm.name}</span>
                  </label>
                ))}
              </div>
            </div>
          ))}
        </div>

        <DialogFooter className="mt-6 flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded-lg bg-100 hover:bg-400">
            Cancel
          </button>
          <button
            onClick={onSave}
            className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 flex items-center gap-2">
            {isEdit ? (
              <Check className="w-4 h-4" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            <span>{isEdit ? "Save" : "Create"}</span>
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RoleDialog;
