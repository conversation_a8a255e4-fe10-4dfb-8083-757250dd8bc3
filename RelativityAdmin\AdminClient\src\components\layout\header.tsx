import React from "react";
import { Building2, User, Settings } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { LanguageSelector, ThemeToggleButton, UserProfile } from "@relativity/shared";
import { useLanguage } from "@relativity/shared/dist/i18n/LanguageProvider";

interface HeaderProps {
  currentUser?: {
    name: string;
    email: string;
    role: string;
  };
}

const Header: React.FC<HeaderProps> = ({ currentUser }) => {
  const { t } = useLanguage();
  const { logout } = useAuth();
  const { toast } = useToast();

  const handleLogout = () => {
    logout();
    toast({
      title: "Logged Out",
      description: "You have been logged out successfully.",
    });
  };

  return (
    <header className="bg-background border-b border-border px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Building2 className="h-8 w-8 text-primary" />
            <h1 className="text-2xl font-bold text-foreground">
              {t("header.title")}
            </h1>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* <LanguageToggle /> */}
          <LanguageSelector variant="select" />
          <ThemeToggleButton />
          <UserProfile
            onLogout={handleLogout}
          />
        </div>
      </div>
    </header>
  );
};

export default Header;
