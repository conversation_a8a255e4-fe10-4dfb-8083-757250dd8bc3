
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Shield } from 'lucide-react';
import { authStats } from './constants';

const AuthenticationStatistics: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="mr-2 h-5 w-5 text-blue-600" />
          Authentication Statistics
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {authStats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
              <p className="text-sm text-gray-600 mb-2">{stat.label}</p>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all"
                  style={{ width: `${stat.percentage}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 mt-1">{stat.percentage}%</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default AuthenticationStatistics;
