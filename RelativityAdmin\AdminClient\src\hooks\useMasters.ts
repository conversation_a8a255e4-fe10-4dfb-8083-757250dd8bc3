import {
  Applications,
  City,
  Country,
  County,
  State,
  ZipCode,
} from "@/lib/Schemas/Master";
import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@/lib/axiosInterceptor";

// Environment variables for API configuration
const API_BASE_URL =
  import.meta.env.VITE_ADMIN_API_BASE_URL || "http://localhost:5007";

// Query Keys
const masterKeys = {
  all: ['masters'] as const,
  applications: () => [...masterKeys.all, 'applications'] as const,
  zipcode: (search?: string, limit?: number) => [...masterKeys.all, 'zipcode', { search, limit }] as const,
  state: (search?: string, limit?: number) => [...masterKeys.all, 'state', { search, limit }] as const,
  country: (search?: string, limit?: number) => [...masterKeys.all, 'country', { search, limit }] as const,
  county: (search?: string, limit?: number) => [...masterKeys.all, 'county', { search, limit }] as const,
  city: (search?: string, limit?: number) => [...masterKeys.all, 'city', { search, limit }] as const,
};

// API functions
const mastersApi = {
  // Get all applications
  getApplications: async (): Promise<Applications[]> => {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/api/masters/applications`);
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to fetch applications");
    }
  },

  // Get zip codes with search and limit
  getZipCode: async (search = "", limit = 50): Promise<ZipCode[]> => {
    try {
      const params = new URLSearchParams();
      if (search) params.append("search", search);
      if (limit) params.append("limit", limit.toString());

      const response = await apiClient.get(
        `${API_BASE_URL}/api/masters/zipcode?${params.toString()}`
      );
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to fetch zipcode");
    }
  },

  // Get states with search and limit
  getState: async (search = "", limit = 50): Promise<State[]> => {
    try {
      const params = new URLSearchParams();
      if (search) params.append("search", search);
      if (limit) params.append("limit", limit.toString());

      const response = await apiClient.get(
        `${API_BASE_URL}/api/masters/state?${params.toString()}`
      );
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to fetch state");
    }
  },

  // Get countries with search and limit
  getCountry: async (search = "", limit = 50): Promise<Country[]> => {
    try {
      const params = new URLSearchParams();
      if (search) params.append("search", search);
      if (limit) params.append("limit", limit.toString());

      const response = await apiClient.get(
        `${API_BASE_URL}/api/masters/country?${params.toString()}`
      );
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to fetch country");
    }
  },

  // Get counties with search and limit
  getCounty: async (search = "", limit = 50): Promise<County[]> => {
    try {
      const params = new URLSearchParams();
      if (search) params.append("search", search);
      if (limit) params.append("limit", limit.toString());

      const response = await apiClient.get(
        `${API_BASE_URL}/api/masters/county?${params.toString()}`
      );
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to fetch county");
    }
  },

  // Get cities with search and limit
  getCity: async (search = "", limit = 50): Promise<City[]> => {
    try {
      const params = new URLSearchParams();
      if (search) params.append("search", search);
      if (limit) params.append("limit", limit.toString());

      const response = await apiClient.get(
        `${API_BASE_URL}/api/masters/city?${params.toString()}`
      );
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to fetch city");
    }
  },
};

// ----------- Queries -----------

// Get all applications
export const useApplications = () => {
  return useQuery({
    queryKey: masterKeys.applications(),
    queryFn: mastersApi.getApplications,
    select: (applications) =>
      applications.map((app: any) => ({
        ...app,
        id: app.id ?? app._id,
        value: app.value,
        label: app.applicationName,
        description: app.description,
      })),
  });
};

// Get zip codes with search
export const useZipCodes = (search = "", limit = 50) => {
  return useQuery({
    queryKey: masterKeys.zipcode(search, limit),
    queryFn: () => mastersApi.getZipCode(search, limit),
    enabled: search.length >= 2, // Only search when at least 2 characters
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get states with search
export const useStates = (search = "", limit = 50) => {
  return useQuery({
    queryKey: masterKeys.state(search, limit),
    queryFn: () => mastersApi.getState(search, limit),
    enabled: search.length >= 2, // Only search when at least 2 characters
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get countries with search
export const useCountries = (search = "", limit = 50) => {
  return useQuery({
    queryKey: masterKeys.country(search, limit),
    queryFn: () => mastersApi.getCountry(search, limit),
    enabled: search.length >= 2, // Only search when at least 2 characters
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get counties with search
export const useCounties = (search = "", limit = 50) => {
  return useQuery({
    queryKey: masterKeys.county(search, limit),
    queryFn: () => mastersApi.getCounty(search, limit),
    enabled: search.length >= 2, // Only search when at least 2 characters
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get cities with search
export const useCities = (search = "", limit = 50) => {
  return useQuery({
    queryKey: masterKeys.city(search, limit),
    queryFn: () => mastersApi.getCity(search, limit),
    enabled: search.length >= 2, // Only search when at least 2 characters
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Backward compatibility exports
export const useGetApplications = useApplications;
export const api = mastersApi;
