// src/lib/axiosInterceptor.ts

import axios, { InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

/**
 * Axios instance with interceptors for Authorization headers and error handling.
 * Usage: import { apiClient } from '@/lib/axiosInterceptor' and use apiClient instead of fetch.
 */

// Create axios instance
export const apiClient = axios.create({
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
});



// Request interceptor to add Authorization header
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = localStorage.getItem("access_token");
    debugger
    if (token) {
      // Ensure headers object exists
      if (!config.headers) {
        config.headers = {} as any;
      }

      // Set the Authorization header
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error: AxiosError) => {
    debugger
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    debugger
    // Handle common errors
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem("access_token");
      localStorage.removeItem("refresh_token");
      localStorage.removeItem("auth_user");

      // Redirect to login page
      window.location.href = `${import.meta.env.VITE_LOGIN_URL}/login?redirectUrl=${encodeURIComponent(window.location.href)}`;
    }

    return Promise.reject(error);
  }
);


/**
 * Test function to verify token and interceptor functionality
 */
export function testTokenInterceptor() {
  const token = localStorage.getItem("access_token");

  return {
    hasToken: !!token,
    tokenLength: token?.length || 0,
    tokenPreview: token ? `${token.substring(0, 20)}...` : 'No token found',
    localStorage: {
      access_token: !!localStorage.getItem("access_token"),
      refresh_token: !!localStorage.getItem("refresh_token"),
      auth_user: !!localStorage.getItem("auth_user")
    }
  };
}
