
import React, { useState } from 'react';
import { Di<PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';

interface BlockchainConfigModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const BlockchainConfigModal: React.FC<BlockchainConfigModalProps> = ({ open, onOpenChange }) => {
  const { toast } = useToast();
  const [settings, setSettings] = useState({
    walletConnectEnabled: false,
    digitalSignaturesEnabled: false,
    identityVerificationEnabled: false,
    supportedNetworks: ['ethereum', 'polygon'],
    requireVerification: false
  });

  const handleSave = () => {
    toast({
      title: "Blockchain Configuration Saved",
      description: "Blockchain authentication settings have been updated successfully.",
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Blockchain Authentication Configuration</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-700">
              <strong>Note:</strong> Blockchain authentication is optional and can be used alongside traditional authentication methods.
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Blockchain Features</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="wallet">Wallet Connect</Label>
                  <p className="text-sm text-gray-600">Allow users to authenticate with crypto wallets</p>
                </div>
                <Switch
                  id="wallet"
                  checked={settings.walletConnectEnabled}
                  onCheckedChange={(checked) => setSettings({ ...settings, walletConnectEnabled: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="signatures">Digital Signatures</Label>
                  <p className="text-sm text-gray-600">Verify identity through digital signatures</p>
                </div>
                <Switch
                  id="signatures"
                  checked={settings.digitalSignaturesEnabled}
                  onCheckedChange={(checked) => setSettings({ ...settings, digitalSignaturesEnabled: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="verification">Identity Verification</Label>
                  <p className="text-sm text-gray-600">Enhanced verification using blockchain</p>
                </div>
                <Switch
                  id="verification"
                  checked={settings.identityVerificationEnabled}
                  onCheckedChange={(checked) => setSettings({ ...settings, identityVerificationEnabled: checked })}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Supported Networks</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Badge variant="default">Ethereum</Badge>
                <Badge variant="default">Polygon</Badge>
                <Badge variant="secondary">Binance Smart Chain</Badge>
                <Badge variant="secondary">Avalanche</Badge>
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="require">Require Blockchain Verification</Label>
                <Switch
                  id="require"
                  checked={settings.requireVerification}
                  onCheckedChange={(checked) => setSettings({ ...settings, requireVerification: checked })}
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Configuration
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BlockchainConfigModal;
