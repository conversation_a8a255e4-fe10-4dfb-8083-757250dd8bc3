
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface AuthenticationMethodsSectionProps {
  onTwoFAOpen: () => void;
  onBlockchainOpen: () => void;
}

const AuthenticationMethodsSection: React.FC<AuthenticationMethodsSectionProps> = ({
  onTwoFAOpen,
  onBlockchainOpen,
}) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Two-Factor Authentication</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span>SMS Authentication</span>
              <Badge>Enabled</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span>Email Authentication</span>
              <Badge>Enabled</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span>Authenticator Apps</span>
              <Badge>Enabled</Badge>
            </div>
            <Button 
              className="w-full mt-4"
              onClick={onTwoFAOpen}
            >
              Configure 2FA Settings
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Blockchain Authentication</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span>Wallet Connect</span>
              <Badge variant="secondary">Optional</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span>Digital Signatures</span>
              <Badge variant="secondary">Optional</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span>Identity Verification</span>
              <Badge variant="secondary">Optional</Badge>
            </div>
            <Button 
              variant="outline" 
              className="w-full mt-4"
              onClick={onBlockchainOpen}
            >
              Configure Blockchain Auth
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthenticationMethodsSection;
