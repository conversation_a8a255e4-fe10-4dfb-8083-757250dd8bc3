
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';

interface SSOConfigurationSectionProps {
  onSSOConfigure: (type: 'saml' | 'oauth' | 'openid') => void;
}

const SSOConfigurationSection: React.FC<SSOConfigurationSectionProps> = ({ onSSOConfigure }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Single Sign-On Configuration</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-blue-600 mr-2" />
              <h3 className="font-semibold text-blue-900">SSO Ready for Integration</h3>
            </div>
            <p className="text-blue-700 mt-2">
              Your system is configured to support SSO integration. Organizations can connect their existing identity providers.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border border-gray-200 rounded-lg p-4 text-center">
              <h4 className="font-semibold">SAML 2.0</h4>
              <p className="text-sm text-gray-600 mt-2">Enterprise identity providers</p>
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-3"
                onClick={() => onSSOConfigure('saml')}
              >
                Configure
              </Button>
            </div>
            <div className="border border-gray-200 rounded-lg p-4 text-center">
              <h4 className="font-semibold">OAuth 2.0</h4>
              <p className="text-sm text-gray-600 mt-2">Google, Microsoft, etc.</p>
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-3"
                onClick={() => onSSOConfigure('oauth')}
              >
                Configure
              </Button>
            </div>
            <div className="border border-gray-200 rounded-lg p-4 text-center">
              <h4 className="font-semibold">OpenID Connect</h4>
              <p className="text-sm text-gray-600 mt-2">Modern authentication</p>
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-3"
                onClick={() => onSSOConfigure('openid')}
              >
                Configure
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SSOConfigurationSection;
