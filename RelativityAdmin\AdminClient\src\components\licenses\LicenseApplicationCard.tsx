import React, { useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Edit } from 'lucide-react';
import { LicensePackage } from './types';

interface LicenseApplicationCardProps {
  applicationName: string;
  appData: LicensePackage;
  onEditClick: (packageData: LicensePackage) => void;
}

const LicenseApplicationCard: React.FC<LicenseApplicationCardProps> = ({
  applicationName,
  appData,
  onEditClick
}) => {
  const packages = appData.package || [];

  const overallStatus = useMemo(() => {
    return packages.some(pkg => pkg.status === 'Active') ? 'Active' : 'Inactive';
  }, [packages]);

  const statusClass = (status: string) => {
    return status === 'Active'
      ? 'bg-green-100 text-green-800'
      : 'bg-gray-100 text-gray-800';
  };

  const getCycleMultiplier = (cycle: string) => {
    return cycle === 'quarterly' ? 3 : cycle === 'annual' ? 12 : 1;
  };

  const formattedDate = useMemo(() => {
    return new Date(appData.createdAt).toLocaleDateString();
  }, [appData.createdAt]);

  const handleEditClick = () => onEditClick(appData);

  if (packages.length === 0) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">{applicationName}</CardTitle>
              <p className="text-sm text-gray-500 mt-1">Created: {formattedDate}</p>
            </div>
            <Badge className="bg-gray-100 text-gray-800">No Packages</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <p>No billing packages available for this application.</p>
            <Button variant="outline" className="mt-4" onClick={handleEditClick}>
              Add Packages
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl">{applicationName}</CardTitle>
            <p className="text-sm text-gray-500 mt-1">Created: {formattedDate}</p>
            {packages[0].description && (
              <p className="text-sm text-gray-600 mt-2">{packages[0].description}</p>
            )}
          </div>
          <Badge className={statusClass(overallStatus)}>{overallStatus}</Badge>
        </div>
      </CardHeader>

      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Billing Cycle</TableHead>
              <TableHead>Price per User</TableHead>
              <TableHead>Minimum Users</TableHead>
              <TableHead>Min. Package Price</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {packages.map(pkg => {
              const multiplier = getCycleMultiplier(pkg.cycleLength);
              const totalPrice = pkg.minimumUsers * pkg.pricePerUser * multiplier;

              return (
                <TableRow key={pkg.cycleLength}>
                  <TableCell className="capitalize font-medium">{pkg.cycleLength}</TableCell>
                  <TableCell>
                    <span className="font-semibold text-green-600">
                      ${pkg.pricePerUser}/month
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="font-semibold text-purple-600">{pkg.minimumUsers}</span>
                  </TableCell>
                  <TableCell>
                    <span className="font-semibold text-orange-600">${totalPrice}</span>
                    <span className="text-xs text-gray-500 ml-1">
                      per {pkg.cycleLength}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Badge className={statusClass(pkg.status)}>{pkg.status}</Badge>
                  </TableCell>
                  <TableCell>
                    <Button variant="outline" size="sm" onClick={handleEditClick}>
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default LicenseApplicationCard;
