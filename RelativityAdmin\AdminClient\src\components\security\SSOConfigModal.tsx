
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

interface SSOConfigModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type?: 'saml' | 'oauth' | 'openid';
}

const SSOConfigModal: React.FC<SSOConfigModalProps> = ({ open, onOpenChange, type = 'saml' }) => {
  const { toast } = useToast();
  const [enabled, setEnabled] = useState(false);
  const [config, setConfig] = useState({
    entityId: '',
    ssoUrl: '',
    certificate: '',
    clientId: '',
    clientSecret: '',
    redirectUri: ''
  });

  const handleSave = () => {
    toast({
      title: "Configuration Saved",
      description: `${type.toUpperCase()} configuration has been updated successfully.`,
    });
    onOpenChange(false);
  };

  const getTitle = () => {
    switch (type) {
      case 'saml': return 'SAML 2.0 Configuration';
      case 'oauth': return 'OAuth 2.0 Configuration';
      case 'openid': return 'OpenID Connect Configuration';
      default: return 'SSO Configuration';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Label htmlFor="enabled">Enable {type.toUpperCase()}</Label>
            <Switch
              id="enabled"
              checked={enabled}
              onCheckedChange={setEnabled}
            />
          </div>

          {enabled && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Configuration Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {type === 'saml' && (
                  <>
                    <div>
                      <Label htmlFor="entityId">Entity ID</Label>
                      <Input
                        id="entityId"
                        value={config.entityId}
                        onChange={(e) => setConfig({ ...config, entityId: e.target.value })}
                        placeholder="https://your-domain.com/saml/metadata"
                      />
                    </div>
                    <div>
                      <Label htmlFor="ssoUrl">SSO URL</Label>
                      <Input
                        id="ssoUrl"
                        value={config.ssoUrl}
                        onChange={(e) => setConfig({ ...config, ssoUrl: e.target.value })}
                        placeholder="https://your-idp.com/sso"
                      />
                    </div>
                    <div>
                      <Label htmlFor="certificate">X.509 Certificate</Label>
                      <textarea
                        id="certificate"
                        className="w-full h-24 px-3 py-2 text-sm border border-gray-300 rounded-md"
                        value={config.certificate}
                        onChange={(e) => setConfig({ ...config, certificate: e.target.value })}
                        placeholder="-----BEGIN CERTIFICATE-----"
                      />
                    </div>
                  </>
                )}

                {(type === 'oauth' || type === 'openid') && (
                  <>
                    <div>
                      <Label htmlFor="clientId">Client ID</Label>
                      <Input
                        id="clientId"
                        value={config.clientId}
                        onChange={(e) => setConfig({ ...config, clientId: e.target.value })}
                        placeholder="your-client-id"
                      />
                    </div>
                    <div>
                      <Label htmlFor="clientSecret">Client Secret</Label>
                      <Input
                        id="clientSecret"
                        type="password"
                        value={config.clientSecret}
                        onChange={(e) => setConfig({ ...config, clientSecret: e.target.value })}
                        placeholder="your-client-secret"
                      />
                    </div>
                    <div>
                      <Label htmlFor="redirectUri">Redirect URI</Label>
                      <Input
                        id="redirectUri"
                        value={config.redirectUri}
                        onChange={(e) => setConfig({ ...config, redirectUri: e.target.value })}
                        placeholder="https://your-app.com/auth/callback"
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          )}

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Configuration
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SSOConfigModal;
