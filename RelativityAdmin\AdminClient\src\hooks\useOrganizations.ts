import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Organization,
  CreateOrganizationData,
  UpdateOrganizationData,
} from "@/lib/Schemas/Organization";
import { apiClient } from "@/lib/axiosInterceptor";

// Environment variables for API configuration
const API_BASE_URL =
  import.meta.env.VITE_ADMIN_API_BASE_URL || "http://localhost:5007";

// API functions for backend communication
const api = {
  // Get all organizations
  getOrganizations: async (): Promise<Organization[]> => {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/api/organizations`);
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to fetch organizations");
    }
  },

  // Get single organization by ID
  getOrganization: async (id: string): Promise<Organization | null> => {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/api/organizations/${id}`);
      return response.data.data; // Extract data property here
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      throw new Error("Failed to fetch organization");
    }
  },

  // Create new organization
  createOrganization: async (
    data: CreateOrganizationData
  ): Promise<Organization> => {
    try {
      const response = await apiClient.post(`${API_BASE_URL}/api/organizations`, data);
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to create organization");
    }
  },

  // Update existing organization
  updateOrganization: async (
    data: UpdateOrganizationData
  ): Promise<Organization> => {
    try {
      const response = await apiClient.put(
        `${API_BASE_URL}/api/organizations/${data._id}`,
        data
      );
      return response.data.data;
    } catch (error) {
      throw new Error("Failed to update organization");
    }
  },

  // Delete organization
  deleteOrganization: async (id: string): Promise<void> => {
    try {
      await apiClient.delete(`${API_BASE_URL}/api/organizations/${id}`);
    } catch (error) {
      throw new Error("Failed to delete organization");
    }
  },
};

// Query keys
const organizationKeys = {
  all: ["organizations"] as const,
  lists: () => [...organizationKeys.all, "list"] as const,
  list: (filters: string) =>
    [...organizationKeys.lists(), { filters }] as const,
  details: () => [...organizationKeys.all, "detail"] as const,
  detail: (_id: string) => [...organizationKeys.details(), _id] as const,
};

// Custom hooks
export const useOrganizations = () => {
  return useQuery<Organization[]>({
    queryKey: organizationKeys.lists(),
    queryFn: api.getOrganizations,
    select: (organizations) =>
      organizations.map((org: any) => ({
        _id: org._id,
        id: org.id ?? org._id,
        organizationName: org.organizationName,
        domain: org.domain,
        numberOfLicenses: org.numberOfLicenses,
        users: org.users ?? 0,
        status: org.status,
        monthlyRevenue: org.monthlyRevenue ?? 0,
        address: {
          streetAddress: org.address.streetAddress,
          city: org.address.city,
          state: org.address.state,
          zipCode: org.address.zipCode,
          country: org.address.country,
        },
        description: org.description,
        is2FAEnabled: org.is2FAEnabled,
        MFAType: org.MFAType,
        selectedAppLicenses: org.selectedAppLicenses,
      })),
  });
};

export const useOrganization = (id: string) => {
  return useQuery({
    queryKey: organizationKeys.detail(id),
    queryFn: () => api.getOrganization(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateOrganization = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: api.createOrganization,
    onSuccess: (newOrganization) => {
      // Normalize the organization object to ensure consistent id field
      const normalizedOrganization = {
        ...newOrganization,
      };

      // Update the organizations list cache
      queryClient.setQueryData<Organization[]>(
        organizationKeys.lists(),
        (old) =>
          old ? [...old, normalizedOrganization] : [normalizedOrganization]
      );

      // Invalidate queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: organizationKeys.lists() });

      console.log("Organization created successfully:", normalizedOrganization);
    },
    onError: (error) => {
      console.error("Error creating organization:", error);
    },
  });
};

export const useUpdateOrganization = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: api.updateOrganization,
    onSuccess: (updatedOrganizationRaw) => {
      // Normalize the organization object to ensure consistent id field
      const updatedOrganization = {
        ...updatedOrganizationRaw,
      };

      // Update single record in detail cache
      queryClient.setQueryData(
        organizationKeys.detail(updatedOrganization._id),
        updatedOrganization
      );

      // Update the list cache
      queryClient.setQueryData<Organization[]>(
        organizationKeys.lists(),
        (old) => {
          if (!old) return [updatedOrganization];

          return old.map((org) => {
            // Check both id and _id to handle potential inconsistencies
            const orgId = org._id;
            const updatedId = updatedOrganization._id;

            return orgId === updatedId ? updatedOrganization : org;
          });
        }
      );

      // Invalidate related queries to ensure consistency
      queryClient.invalidateQueries({
        queryKey: organizationKeys.detail(updatedOrganization._id),
      });
      queryClient.invalidateQueries({ queryKey: organizationKeys.lists() });

      console.log("Organization updated successfully:", updatedOrganization);
    },
    onError: (error) => {
      console.error("Error updating organization:", error);
    },
  });
};

export const useDeleteOrganization = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: api.deleteOrganization,
    onSuccess: (_, deletedId) => {
      // Remove the organization from list cache
      queryClient.setQueryData<Organization[]>(
        organizationKeys.lists(),
        (old) => {
          if (!old) return [];

          return old.filter((org) => {
            const orgId = org._id;
            return orgId !== deletedId;
          });
        }
      );

      // Remove the specific organization from detail cache
      queryClient.removeQueries({
        queryKey: organizationKeys.detail(deletedId),
      });

      // Invalidate list queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: organizationKeys.lists() });

      console.log("Organization deleted successfully:", deletedId);
    },
    onError: (error) => {
      console.error("Error deleting organization:", error);
    },
  });
};

// Utility hook for organization statistics
export const useOrganizationStats = () => {
  const { data: organizations = [] } = useOrganizations();

  return {
    totalOrganizations: organizations.length,
    totalLicenses: organizations.reduce(
      (sum, org) => sum + org.numberOfLicenses,
      0
    ),
    averageLicenses:
      organizations.length > 0
        ? Math.round(
          organizations.reduce((sum, org) => sum + org.numberOfLicenses, 0) /
          organizations.length
        )
        : 0,
    organizationsByCountry: organizations.reduce((acc, org) => {
      const country = org.address.country;
      acc[country] = (acc[country] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
  };
};
