import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Building2,
  Users,
  CreditCard,
  Settings,
  UserPlus,
  Trash2,
  AlertTriangle,
  LockIcon,
} from "lucide-react";
import {
  Organization,
  OrganizationManageModalProps,
  OrganizationStatus,
} from "@/lib/Schemas/Organization";
import { OTPCodeType } from "@/lib/Schemas/Master";
import AppLicenseSelector from "../licenses/AppLicenseSelector";
import { useLanguage } from "@relativity/shared/dist/i18n/LanguageProvider";

const OrganizationManageModal: React.FC<OrganizationManageModalProps> = ({
  organization,
  open,
  onOpenChange,
  onUpdate,
  licensePackages,
}) => {
  const { t } = useLanguage();
  const [editedOrg, setEditedOrg] = useState<Organization | null>(organization);
  const [activeTab, setActiveTab] = useState<
    "general" | "licenses" | "users" | "billing" | "2fa"
  >("general");
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);
  const [selectedMethods, setSelectedMethods] = useState<string[]>([]);
  const [selectedAppLicenses, setselectedAppLicenses] = useState<string[]>([]);

  React.useEffect(() => {
    if (!organization) return;
    setEditedOrg(organization);
    setIs2FAEnabled(organization?.is2FAEnabled);
    setSelectedMethods(organization?.MFAType || []);
    setActiveTab("general");
  }, [organization]);

  if (!organization || !editedOrg) return null;

  const handleSave = () => {
    debugger;
    editedOrg.MFAType = selectedMethods as OTPCodeType[];
    editedOrg.is2FAEnabled = is2FAEnabled;
    editedOrg.selectedAppLicenses = selectedAppLicenses;
    editedOrg._id = editedOrg._id || organization._id; // Ensure _id is set
    onUpdate(editedOrg);
    onOpenChange(false);
  };

  const handleAddLicenses = () => {
    const newLicenses = prompt("How many licenses to add?");
    if (newLicenses && !isNaN(Number(newLicenses))) {
      const additionalLicenses = Number(newLicenses);
      setEditedOrg({
        ...editedOrg,
        numberOfLicenses: editedOrg.numberOfLicenses + additionalLicenses,
        monthlyRevenue: editedOrg.monthlyRevenue + additionalLicenses * 75,
      });
    }
  };

  const selectedAppChanges = (data) => {
    debugger;
    setselectedAppLicenses(data);
  };

  const toggleMethod = (method: string) => {
    setSelectedMethods((prev) =>
      prev.includes(method)
        ? prev.filter((m) => m !== method)
        : [...prev, method]
    );
  };

  const handleStatusChange = (newStatus: OrganizationStatus) => {
    setEditedOrg({
      ...editedOrg,
      status: newStatus,
    });
  };

  const getStatusColor = (status: OrganizationStatus) => {
    switch (status) {
      case OrganizationStatus.Active:
        return "bg-green-100 text-green-800";
      case OrganizationStatus.Pending:
        return "bg-yellow-100 text-yellow-800";
      case OrganizationStatus.Suspended:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-800";
    }
  };

  const tabs = [
    { id: "general", label: t("organizations.general"), icon: Settings },
    { id: "licenses", label: t("organizations.licenses"), icon: CreditCard },
    { id: "users", label: t("organizations.users"), icon: Users },
    { id: "billing", label: t("roles.billing"), icon: Building2 },
    { id: "2fa", label: t("organizations.2fa"), icon: LockIcon },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5 text-blue-600" />
            <span>{t('organizations.manage')} {organization.organizationName}</span>
          </DialogTitle>
        </DialogHeader>

        {/* Tabs */}
        <div className="flex border-b">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? "border-blue-600 text-blue-600"
                    : "border-transparent text-500 hover:text-700"
                }`}>
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        <div className="space-y-6 mt-4">
          {/* General Tab */}
          {activeTab === "general" && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="orgName">{t('organizations.organizationName')}</Label>
                  <Input
                    id="orgName"
                    value={editedOrg.organizationName}
                    onChange={(e) =>
                      setEditedOrg({
                        ...editedOrg,
                        organizationName: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="domain">{t('organizations.domain')}</Label>
                  <Input
                    id="domain"
                    value={editedOrg.domain}
                    onChange={(e) =>
                      setEditedOrg({ ...editedOrg, domain: e.target.value })
                    }
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>{t('organizations.status')}</Label>
                <div className="flex space-x-2">
                  {Object.values(OrganizationStatus).map((status) => (
                    <Button
                      key={status}
                      variant={
                        editedOrg.status === status ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() => handleStatusChange(status)}>
                      {status}
                    </Button>
                  ))}
                </div>
                <Badge className={getStatusColor(editedOrg.status)}>
                  {t('organizations.current')}: {editedOrg.status}
                </Badge>
              </div>
            </div>
          )}

          {/* Licenses Tab */}
          {activeTab === "licenses" && (
            <div className="space-y-4">
              <div className="bg-50 pt-0 pb-4 pl-4 pr-4 rounded-lg">
                <AppLicenseSelector
                  selectedAppLicenses={selectedAppChanges}
                  existSelectedAppLicenses={editedOrg.selectedAppLicenses}
                  licensePackages={licensePackages}></AppLicenseSelector>
              </div>

              {/* <div className="flex space-x-2">
                <Button
                  onClick={handleAddLicenses}
                  className="flex items-center space-x-2">
                  <UserPlus className="h-4 w-4" />
                  <span>Add Licenses</span>
                </Button>
                <Button variant="outline">Modify License Package</Button>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-blue-800">
                  Each license costs $75/user/month. Current monthly cost: $
                  {(editedOrg.numberOfLicenses * 75).toLocaleString()}
                </p>
              </div> */}
            </div>
          )}

          {/* Users Tab */}
          {activeTab === "users" && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">{t("userMgmt.title")}</h3>
                <Button size="sm">{t("userMgmt.addUser")}</Button>
              </div>

              <div className="border rounded-lg p-4">
                <p className="text-sm text-600 mb-2">
                  {t("dashboard.activeUsers")} {editedOrg.users}
                </p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{
                      width: `${
                        (editedOrg.users / editedOrg.numberOfLicenses) * 100
                      }%`,
                    }}></div>
                </div>
                <p className="text-xs text-500 mt-1">
                  {Math.round(
                    (editedOrg.users / editedOrg.numberOfLicenses) * 100
                  )}
                  {t("userMgmt.percentLicensesUsed")}
                </p>
              </div>

              <div className="space-y-2">
                <Button variant="outline" size="sm">
                  {t("userMgmt.exportUserList")}
                </Button>
                <Button variant="outline" size="sm">
                  {t("userMgmt.bulkUserOperations")}
                </Button>
                <Button variant="outline" size="sm">
                  {t("userMgmt.userAccessReport")}
                </Button>
              </div>
            </div>
          )}

          {/* Billing Tab */}
          {activeTab === "billing" && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-900">
                    {t("organizations.monthlyRevenue")}
                  </h4>
                  <p className="text-2xl font-bold text-green-600">
                    ${editedOrg.monthlyRevenue.toLocaleString()}
                  </p>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-900">
                    {t("organizations.annualRevenue")}
                  </h4>
                  <p className="text-2xl font-bold text-blue-600">
                    ${(editedOrg.monthlyRevenue * 12).toLocaleString()}
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">{t("billing.billingActions")}</h4>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    {t("billing.generateInvoice")}
                  </Button>
                  <Button variant="outline" size="sm">
                    {t("billing.paymentHistory")}
                  </Button>
                  <Button variant="outline" size="sm">
                    {t("billing.updateBillingInfo")}
                  </Button>
                </div>
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg">
                <p className="text-sm text-yellow-800">
                  {t("organizations.nextBillingDate")}: February 15, 2024
                </p>
              </div>
            </div>
          )}

          {/* 2FA Tab */}
          {activeTab === "2fa" && (
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-semibold">{t("2fa.multiAuth")}</h4>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="enable2FA"
                    checked={is2FAEnabled}
                    onChange={() => {
                      setIs2FAEnabled((prev) => !prev);
                      if (is2FAEnabled) setSelectedMethods([]); // Reset methods if 2FA is turned off
                    }}
                  />
                  <label htmlFor="enable2FA">{t("2fa.enable2FA")}</label>
                </div>
              </div>

              {is2FAEnabled && (
                <div className="space-y-2">
                  <h4 className="font-semibold">{t("2fa.authenticationMethods")}</h4>
                  <div className="flex flex-col space-y-2">
                    {Object.values(OTPCodeType).map((method) => (
                      <label
                        key={method}
                        className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={selectedMethods.includes(method)}
                          onChange={() => toggleMethod(method)}
                        />
                        <span>{method}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {t("common.cancel")}
            </Button>
            <Button onClick={handleSave}>{t("common.save")}</Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default OrganizationManageModal;
