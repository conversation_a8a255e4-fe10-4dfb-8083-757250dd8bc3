import React, { createContext, useContext, useState, useEffect } from "react";
import { clearAuth, getStoredAuth, storeAuth } from "../lib/auth";
import { AuthContextType, checkAuthValidity } from "@/lib/Schemas/Master";
import { useNavigate } from "react-router-dom";
import { ApplicationName } from "@/lib/Schemas/Personnel";
import { apiClient } from "@/lib/axiosInterceptor";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({
  children,
}: {
  children: React.ReactNode;
}): JSX.Element {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true); // Start with loading true
  const [authState, setAuthState] = useState(() => {
    return getStoredAuth();
  });

  const validateToken = async (refreshToken: string): Promise<checkAuthValidity> => {
    try {
      const res = await apiClient.post(
        `${import.meta.env.VITE_LOGIN_API_BASE_URL}/api/auth/checkAuthValidity`,
        {
          refreshToken: refreshToken,
          application: ApplicationName.RELATIVITY_ADMIN,
        }
      );

      if (res.status === 200) {
        return res.data;
      }
    } catch (error: any) {
      if (error.response?.status === 403) {
        //Do not have access to this app
        navigate(`/not-found?refreshToken=${encodeURIComponent(refreshToken)}`, {
          replace: true,
        });
        return null;
      }
      if (error.response?.status === 401) {
        return {
          success: false,
          message: "Token is invalid",
          user: null,
          accessToken: null,
          refreshToken: null,
        };
      }

      console.error("Token validation failed: Unexpected response");
      return {
        success: false,
        message: "Token is invalid",
        user: null,
        accessToken: null,
        refreshToken: null,
      };
    }
  };

  const clearAuthAndRedirect = async () => {
    await clearAuth();
    setAuthState({
      accessToken: null,
      isAuthenticated: false,
      user: null,
      refreshToken: null,
    });

    window.location.href = `${import.meta.env.VITE_LOGIN_URL
      }/login?redirectUrl=${encodeURIComponent(window.location.href)}`;
  };

  useEffect(() => {
    const initAuth = async () => {
      setIsLoading(true);

      const urlParams = new URLSearchParams(window.location.search);
      const refreshToken = urlParams.get("refreshToken");

      if (refreshToken) {
        // Clean URL without full page reload
        const url = new URL(window.location.href);
        url.searchParams.delete("refreshToken");
        window.history.replaceState(
          {},
          document.title,
          url.pathname + url.search
        );
        const res = await validateToken(refreshToken);
        if (!res) return;
        if (res.success) {
          await storeAuth(res.user, res.accessToken, res.refreshToken);
          setAuthState(await getStoredAuth());
        } else {
          await clearAuthAndRedirect();
          return;
        }
      } else {
        const stored = getStoredAuth();
        if (stored.refreshToken) {
          const res = await validateToken(stored.refreshToken);
          if (res.success) {
            await storeAuth(res.user, res.accessToken, res.refreshToken);
            setAuthState(stored);
          } else {
            await clearAuthAndRedirect();
            return;
          }
        } else {
          await clearAuthAndRedirect();
          return;
        }
      }

      setIsLoading(false);
    };

    initAuth();
  }, []);

  const logout = async (socketLogoutFn?: () => Promise<void>) => {
    setIsLoading(true);

    try {
      // If socket logout function is provided, use it first
      if (socketLogoutFn) {
        console.log("🔌 Performing socket logout...");
        await socketLogoutFn();
      }
    } catch (error) {
      console.error("❌ Socket logout failed:", error);
      // Continue with regular logout even if socket logout fails
    }

    // Clear local auth state
    await clearAuth();
    setAuthState({
      accessToken: null,
      isAuthenticated: false,
      user: null,
      refreshToken: null,
    });

    // Redirect to login
    window.location.href = `${import.meta.env.VITE_LOGIN_URL}?logout=true`;
    setIsLoading(false);
  };

  return React.createElement(
    AuthContext.Provider,
    {
      value: {
        ...authState,
        logout,
        isLoading: isLoading,
      },
    },
    children
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
