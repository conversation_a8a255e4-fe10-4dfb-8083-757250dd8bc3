import { Person } from "./Personnel";

export interface Applications {
  _id?: string;
  id: string;
  value: string;
  label: string;
}

export interface ZipCode {
  ZCTA: "TRUE" | "FALSE";
  Population: number;
  Density: number;
  Imprecise: "TRUE" | "FALSE";
  Military: "TRUE" | "FALSE";
  Timezone: string;
  CityName: string;
  CountyWeights: Record<string, string>; // parsed JSON object
  GeoPoint: string; // could also be turned into { lat: number; lng: number } if you parse it
  OfficialCountyCode: string;
  OfficialCountyName: string;
  PrimaryOfficialCountyCode: string;
  PrimaryOfficialCountyName: string;
  StateCode: string;
  StateName: string;
  ZipCode: string;
}

export interface State {
  StateName: string;
  StateCode: string;
}

export interface Country {
  official_lang_code: string; // e.g., "EN"
  iso2_code: string; // e.g., "KH"
  iso3_code: string; // e.g., "KHM"
  onu_code: string; // UN country numeric code, e.g., "116"
  is_ilomember: "Y" | "N"; // "Y" for yes, "N" for no
  is_receiving_quest: "Y" | "N";
  label_en: string; // Country name in English
  label_fr: string; // Country name in French
  label_sp: string; // Country name in Spanish
  geo_point_2d: {
    lon: number;
    lat: number;
  };
}

export interface County {
  CountyName: string;
  StateName: string;
  StateCode: string;
}

export interface City {
  CityName: string;
  CountyName: string;
  StateName: string;
  StateCode: string;
}

export enum OTPCodeType {
  EMAIL = "Email",
  SMS = "SMS",
  AUTHENTICATOR = "Authenticator",
}

export interface checkAuthValidity {
  success: boolean | false;
  message: string | null;
  user: Person | null;
  accessToken: string | null;
  refreshToken: string | null;
}

export interface AuthContextType {
  isAuthenticated: boolean;
  user: Person | null;
  refreshToken: string | null;
  accessToken: string | null;
  logout: () => void;
  isLoading: boolean;
}
