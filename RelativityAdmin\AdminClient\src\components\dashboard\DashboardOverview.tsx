import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Building2, Users, CreditCard, TrendingUp, Loader2, AlertCircle } from 'lucide-react';
import useDashboardData from '../../hooks/useDashboardData'; // Adjust the import path
import { useLanguage } from "@relativity/shared/dist/i18n/LanguageProvider";

const DashboardOverview: React.FC = () => {
  const {
    data: dashboardData,
    isLoading,
    isError,
    error,
    refetch,
  } = useDashboardData();

  const { t } = useLanguage();

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold text-900">{t('dashboard.title')}</h2>
          <p className="text-600 mt-2">{t('dashboard.subtitle')}</p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>{t('dashboard.loading')}</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold text-900">{t('dashboard.title')}</h2>
          <p className="text-600 mt-2">{t('dashboard.subtitle')}</p>
        </div>
        <Card className="border-red-200">
          <CardContent className="flex items-center space-x-2 p-6">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <div>
              <p className="text-red-700 font-medium">t{('dashboard.error')}</p>
              <p className="text-red-600 text-sm">{error?.message}</p>
              <button
                onClick={() => refetch()}
                className="mt-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
              >
                Retry
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // No data state
  if (!dashboardData) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold text-900">{t('dashboard.title')}</h2>
          <p className="text-600 mt-2">{t('dashboard.subtitle')}</p>
        </div>
        <Card>
          <CardContent className="p-6">
            <p className="text-500">{t('dashboard.noData')}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Create stats array from API data
  const stats = [
    {
      title: t('dashboard.totalOrgs'),
      value: dashboardData.overview.totalOrganizations.count.toLocaleString(),
      change: dashboardData.overview.totalOrganizations.growth,
      icon: Building2,
      color: 'text-blue-600',
    },
    {
      title: t('dashboard.activeUsers'),
      value: dashboardData.overview.activeUsers.count.toLocaleString(),
      change: dashboardData.overview.activeUsers.growth,
      icon: Users,
      color: 'text-green-600',
    },
    {
      title: t('dashboard.licenseRevenue'),
      value: dashboardData.overview.licenseRevenue.formatted,
      change: dashboardData.overview.licenseRevenue.growth,
      icon: CreditCard,
      color: 'text-purple-600',
    },
    {
      title: t('dashboard.growthRate'),
      value: dashboardData.overview.growthRate.percentage,
      change: dashboardData.overview.growthRate.description,
      icon: TrendingUp,
      color: 'text-orange-600',
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-900">{t('dashboard.title')}</h2>
          <p className="text-600 mt-2">{t('dashboard.subtitle')}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-600">
                  {stat.title}
                </CardTitle>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-900">{stat.value}</div>
                <p className="text-xs text-500 mt-1">{stat.change}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('dashboard.recentOrgs')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData.recentOrganizations.length > 0 ? (
                dashboardData.recentOrganizations.map((org, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
                  >
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">{org.name}</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{org.users}</p>
                    </div>
                    <span
                      className={
                        `px-2 py-1 rounded-full text-xs font-medium ` +
                        (org.status === 'Active'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : org.status === 'Pending'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200')
                      }
                    >
                      {org.status}
                    </span>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 dark:text-gray-400 text-center py-4">{t('dashboard.noRecentOrgs')}</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('dashboard.licenseUsage')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{t('dashboard.licensesSold')}</span>
                <span className="font-bold text-gray-900">
                  {dashboardData.licenseUsage.totalLicensesSold.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{t('dashboard.activeUsers')}</span>
                <span className="font-bold text-gray-900">
                  {dashboardData.licenseUsage.activeUsers.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{t('dashboard.utilizationRate')}</span>
                <span className="font-bold text-green-600">
                  {dashboardData.licenseUsage.utilizationRate.percentage}
                </span>
              </div>
              <div className="mt-4">
                <div className="bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-500"
                    style={{
                      width: `${Math.min(dashboardData.licenseUsage.utilizationRate.decimal * 100, 100)}%`
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DashboardOverview;