import React, { useEffect, useState } from "react";
import { Plus, Minus, Check, Shield, Users } from "lucide-react";
import { Organization } from "@/lib/Schemas/Organization";
import { useLanguage } from "@relativity/shared/dist/i18n/LanguageProvider";

export interface SelectedApps {
  [_id: string]: {
    licenses: number;
  };
}

export interface SelectedAppOutput {
  _id: string;
  applicationName: string;
  displayName?: string;
  description: string;
  icon: string;
  licenses: number;
}

interface AppLicenseSelectorProps {
  selectedAppLicenses: (data: SelectedAppOutput[]) => void;
  existSelectedAppLicenses?: SelectedAppOutput[];
  licensePackages?: SelectedAppOutput[];
}

const AppLicenseSelector: React.FC<AppLicenseSelectorProps> = ({
  selectedAppLicenses,
  existSelectedAppLicenses = [],
  licensePackages = [],
}) => {
  const { t } = useLanguage();
  const skipAppNames = ["relativityadmin", "organizationadmin"];
  const availableApps = licensePackages.filter(
    (app) => !skipAppNames.includes(app.applicationName)
  );

  // Initialize from existing licenses if passed in
  const initialSelectedApps: SelectedApps = {};
  existSelectedAppLicenses.forEach((app) => {
    initialSelectedApps[app._id] = { licenses: app.licenses };
  });

  const [selectedApps, setSelectedApps] =
    useState<SelectedApps>(initialSelectedApps);

  useEffect(() => {
    notifyParent(initialSelectedApps);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const notifyParent = (apps: SelectedApps = selectedApps) => {
    const output: SelectedAppOutput[] = Object.entries(apps).map(
      ([appId, appData]) => {
        const appInfo = availableApps.find((a) => a._id === appId);
        return {
          _id: appId,
          applicationName: appInfo?.applicationName || "",
          displayName: appInfo?.displayName || appInfo?.applicationName || "",
          description: appInfo?.description || "",
          icon: appInfo?.icon || "",
          licenses: appData.licenses,
        };
      }
    );
    selectedAppLicenses(output);
  };

  const handleAppToggle = (appId: string) => {
    setSelectedApps((prev) => {
      const newSelected = { ...prev };
      if (newSelected[appId]) {
        delete newSelected[appId];
      } else {
        newSelected[appId] = { licenses: 1 };
      }
      notifyParent(newSelected);
      return newSelected;
    });
  };

  const handleLicenseChange = (appId: string, licenses: string) => {
    const numLicenses = parseInt(licenses, 10) || 1;
    setSelectedApps((prev) => {
      const updated = {
        ...prev,
        [appId]: { licenses: numLicenses },
      };
      notifyParent(updated);
      return updated;
    });
  };

  const incrementLicense = (appId: string) => {
    setSelectedApps((prev) => {
      const updated = {
        ...prev,
        [appId]: { licenses: (prev[appId]?.licenses || 0) + 1 },
      };
      notifyParent(updated);
      return updated;
    });
  };

  const decrementLicense = (appId: string) => {
    setSelectedApps((prev) => {
      const currentLicenses = prev[appId]?.licenses || 0;
      let updated = { ...prev };
      if (currentLicenses > 1) {
        updated[appId] = { licenses: currentLicenses - 1 };
      }
      notifyParent(updated);
      return updated;
    });
  };

  const getTotalLicenses = (): number => {
    return Object.values(selectedApps).reduce(
      (total, app) => total + (app.licenses || 0),
      0
    );
  };

  const getSelectedCount = () => {
    return Object.keys(selectedApps).length;
  };



  return (
    <div className="max-w-4xl mx-auto pt-0 pb-6 pl-6 pr-6 bg-background">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-900 mb-2">
          {t("license.title")}
        </h1>
        <p className="text-600">
          {t("license.subtitle")}
        </p>
      </div>

      {/* Selection Summary */}
      {getSelectedCount() > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-blue-600" />
              <span className="font-medium text-blue-900">
                {getSelectedCount()} {t("license.appsSelected")}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="w-5 h-5 text-blue-600" />
              <span className="font-medium text-blue-900">
                {getTotalLicenses()} {t("license.totalLicenses")}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Applications Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {availableApps.map((app) => {
          const isSelected = !!selectedApps[app._id];
          const licenses = selectedApps[app._id]?.licenses || 1;

          return (
            <div
              key={app._id}
              className={`border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${isSelected
                ? "border-blue-500 bg-50 shadow-md"
                : "border-gray-200 hover:border-gray-300 hover:shadow-sm"
                }`}
              onClick={() => handleAppToggle(app._id)}>
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{app.icon}</span>
                  <div>
                    <h3 className="font-semibold text-900">
                      {app.displayName}
                    </h3>
                  </div>
                </div>
                <div
                  className={`w-5 h-5 rounded border-2 flex items-center justify-center ${isSelected
                    ? "bg-blue-500 border-blue-500"
                    : "border-gray-300"
                    }`}>
                  {isSelected && <Check className="w-3 h-3 text-white" />}
                </div>
              </div>

              <p className="text-sm text-600 mb-3">{app.description}</p>

              {isSelected && (
                <div
                  className="border-t pt-3"
                  onClick={(e) => e.stopPropagation()}>
                  <label className="block text-sm font-medium text-700 mb-2">
                    {t("license.numOfLicenses")}
                  </label>
                  <div className="flex items-center space-x-2">
                    <button
                      type="button"
                      onClick={() => decrementLicense(app._id)}
                      className="w-8 h-8 rounded border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50"
                      disabled={licenses <= 1}>
                      <Minus className="w-4 h-4" />
                    </button>
                    <input
                      type="number"
                      value={licenses}
                      onChange={(e) =>
                        handleLicenseChange(app._id, e.target.value)
                      }
                      min="1"
                      className="w-20 px-2 py-1 text-center border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-background"
                    />
                    <button
                      type="button"
                      onClick={() => incrementLicense(app._id)}
                      className="w-8 h-8 rounded border border-gray-300 flex items-center justify-center hover:bg-gray-50">
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Selected Applications Summary */}
      {getSelectedCount() > 0 && (
        <div className="mt-8 p-4 bg-50 rounded-lg">
          <h3 className="font-medium text-900 mb-3">{t("license.selectedApps")}</h3>
          <div className="space-y-2">
            {Object.entries(selectedApps).map(([appId, appData]) => {
              const app = availableApps.find((a) => a._id === appId);
              return (
                <div
                  key={appId}
                  className="flex justify-between items-center text-sm">
                  <span className="text-700">
                    {app?.icon} {app?.displayName}
                  </span>
                  <span className="font-medium text-900">
                    {appData?.licenses}{" "}
                    {appData?.licenses === 1 ? t("organizations.license") : t("organizations.licenses")}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default AppLicenseSelector;
