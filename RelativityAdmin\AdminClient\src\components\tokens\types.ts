
export interface UserToken {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  organizationId: string;
  organizationName: string;
  licensePackageId: string;
  applicationName: string;
  cycleLength: 'monthly' | 'quarterly' | 'annual';
  issuedDate: string;
  expiryDate: string;
  status: 'Active' | 'Expired' | 'Revoked';
  issuedBy: string;
}

export interface TokenIssuanceRequest {
  userId: string;
  userName: string;
  userEmail: string;
  organizationId: string;
  licensePackageId: string;
}
