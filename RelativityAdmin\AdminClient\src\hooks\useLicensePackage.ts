import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { LicensePackage } from '@/components/licenses/types';
import { apiClient } from '@/lib/axiosInterceptor';

// Environment variables for API configuration
const API_BASE_URL = import.meta.env.VITE_ADMIN_API_BASE_URL || "http://localhost:5007";

// API functions
const licenseApi = {
    // Get all licenses (with pagination and optional search)
    getLicenses: async (page = 1, limit = 100, search = ''): Promise<LicensePackage[]> => {
        try {
            const queryParams = new URLSearchParams({ page: `${page}`, limit: `${limit}` });
            if (search) queryParams.append('search', search);

            const response = await apiClient.get(`${API_BASE_URL}/api/licenses?${queryParams.toString()}`);
            return response.data.data;
        } catch (error) {
            throw new Error("Failed to fetch licenses");
        }
    },

    // Get single license by ID
    getLicense: async (id: string): Promise<LicensePackage | null> => {
        try {
            const response = await apiClient.get(`${API_BASE_URL}/api/licenses/${id}`);
            return response.data.data;
        } catch (error: any) {
            if (error.response?.status === 404) {
                return null;
            }
            throw new Error("Failed to fetch license");
        }
    },

    // Create new license
    createLicense: async (data: Partial<LicensePackage>): Promise<LicensePackage> => {
        try {
            const response = await apiClient.post(`${API_BASE_URL}/api/licenses`, data);
            return response.data.data;
        } catch (error) {
            throw new Error("Failed to create license");
        }
    },

    // Update existing license
    updateLicense: async (id: string, data: Partial<LicensePackage>): Promise<LicensePackage> => {
        try {
            const response = await apiClient.put(`${API_BASE_URL}/api/licenses/${id}`, data);
            return response.data.data;
        } catch (error) {
            throw new Error("Failed to update license");
        }
    },

    // Delete single license
    deleteLicense: async (id: string): Promise<void> => {
        try {
            await apiClient.delete(`${API_BASE_URL}/api/licenses/${id}`);
        } catch (error) {
            throw new Error("Failed to delete license");
        }
    },

    // Delete multiple licenses
    deleteLicensesBulk: async (ids: string[]): Promise<void> => {
        try {
            await apiClient.delete(`${API_BASE_URL}/api/licenses`, { data: { ids } });
        } catch (error) {
            throw new Error("Failed to delete licenses");
        }
    },
};

// Query Keys
const licenseKeys = {
    all: ['licenses'] as const,
    lists: () => [...licenseKeys.all, 'list'] as const,
    list: (page: number, limit: number, search: string) => [...licenseKeys.lists(), page, limit, search] as const,
    details: () => [...licenseKeys.all, 'detail'] as const,
    detail: (id: string) => [...licenseKeys.details(), id] as const,
};

// ----------- Queries -----------

// Get all licenses (with pagination and optional search)
export const useLicenses = (page = 1, limit = 100, search = '') => {
    return useQuery({
        queryKey: licenseKeys.list(page, limit, search),
        queryFn: () => licenseApi.getLicenses(page, limit, search),
    });
};

// Get single license by ID
export const useLicense = (id: string) => {
    return useQuery({
        queryKey: licenseKeys.detail(id),
        queryFn: () => licenseApi.getLicense(id),
        enabled: !!id,
    });
};

// ----------- Mutations -----------

export const useCreateLicense = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (data: Partial<LicensePackage>) => licenseApi.createLicense(data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: licenseKeys.all });
        },
    });
};

export const useUpdateLicense = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, data }: { id: string; data: Partial<LicensePackage> }) =>
            licenseApi.updateLicense(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: licenseKeys.all });
        },
    });
};

export const useDeleteLicense = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (id: string) => licenseApi.deleteLicense(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: licenseKeys.all });
        },
    });
};

export const useDeleteLicensesBulk = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (ids: string[]) => licenseApi.deleteLicensesBulk(ids),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: licenseKeys.all });
        },
    });
};
