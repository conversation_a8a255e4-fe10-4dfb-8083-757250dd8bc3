import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  BrowserRouter,
  Routes,
  Route,
  Navigate,
  useLocation,
} from "react-router-dom";

import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import "./i18n";
import { AuthProvider } from "./hooks/useAuth";
import AuthGuard from "./AuthGuard";
import ClearStorage from "./pages/ClearStorage";
import "@/lib/axiosInterceptor"; // Import to initialize axios interceptors
import { ThemeProvider, LanguageProvider, reactLanguageConfig, LanguageSelector, commonTranslations } from "@relativity/shared";


const queryClient = new QueryClient();

const Router = () => {
  const RedirectToHome = () => {
    const location = useLocation();
    const search = location.search; // preserves ?refreshToken=abc

    return <Navigate to={`/dashboard${search}`} replace />;
  };

  return (
    <Routes>
      {/* Redirect root to dashboard */}
      <Route path="/" element={<RedirectToHome />} />

      {/* Protected dashboard route */}
      <Route
        path="/dashboard"
        element={
          <AuthGuard>
            <Index />
          </AuthGuard>
        }
      />

      <Route path="/not-found" element={<NotFound />} />
      <Route path="/clear" element={<ClearStorage />} />
    </Routes>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem
      disableTransitionOnChange
    >
      <LanguageProvider
        {...reactLanguageConfig}
        translations={commonTranslations}
      >

        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <AuthProvider>
              <Router />
            </AuthProvider>
          </BrowserRouter>
        </TooltipProvider>
      </LanguageProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
