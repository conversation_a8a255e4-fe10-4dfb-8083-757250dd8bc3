import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Package, TrendingUp, DollarSign } from 'lucide-react';
import { LicensePackage } from './types';

interface LicenseSummaryCardsProps {
  licensePackages: LicensePackage[];
  groupedLicenses: Record<string, LicensePackage>;
}

const LicenseSummaryCards: React.FC<LicenseSummaryCardsProps> = ({
  licensePackages,
  groupedLicenses,
}) => {
  // You can now use either licensePackages or groupedLicenses
  // Using groupedLicenses (which is the same data, just keyed by app name):
  const groupedLicenseArray = Object.values(groupedLicenses);
  
  // Calculate total number of individual packages (cycles)
  const totalPackages = groupedLicenseArray.reduce((sum, license) => sum + license?.package?.length, 0);
  
  // Calculate number of active packages
  const activePackages = groupedLicenseArray.reduce((count, license) => {
    return count + license?.package?.filter(pkg => pkg.status === 'Active').length;
  }, 0);

  // Calculate average price per user across all packages
  const totalPricePerUser = groupedLicenseArray.reduce((sum, license) => {
    return sum + license?.package?.reduce((packageSum, pkg) => packageSum + pkg?.pricePerUser, 0);
  }, 0);
  
  const avgPricePerUser = totalPackages > 0 ? Math.round(totalPricePerUser / totalPackages) : 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">License Packages</CardTitle>
          <Package className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900">{totalPackages}</div>
          <p className="text-xs text-gray-500">{activePackages} active</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Applications</CardTitle>
          <DollarSign className="h-4 w-4 text-purple-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900">
            {Object.keys(groupedLicenses).length}
          </div>
          <p className="text-xs text-gray-500">unique applications</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Avg. Price/User</CardTitle>
          <TrendingUp className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900">
            ${avgPricePerUser}
          </div>
          <p className="text-xs text-gray-500">per month</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default LicenseSummaryCards;