
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { securityFeatures } from './constants';

interface SecurityFeaturesGridProps {
  onTwoFAOpen: () => void;
  onBlockchainOpen: () => void;
  onSSOOpen: () => void;
}

const SecurityFeaturesGrid: React.FC<SecurityFeaturesGridProps> = ({
  onTwoFAOpen,
  onBlockchainOpen,
  onSSOOpen,
}) => {
  const handleConfigure = (featureTitle: string) => {
    if (featureTitle === '2-Factor Authentication') {
      onTwoFAOpen();
    } else if (featureTitle === 'Blockchain Authentication') {
      onBlockchainOpen();
    } else {
      onSSOOpen();
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {securityFeatures.map((feature, index) => {
        const Icon = feature.icon;
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center space-y-0 pb-4">
              <div className={`p-2 rounded-lg bg-gray-100 mr-4`}>
                <Icon className={`h-6 w-6 ${feature.color}`} />
              </div>
              <div className="flex-1">
                <CardTitle className="text-lg">{feature.title}</CardTitle>
                <p className="text-sm text-gray-600 mt-1">{feature.description}</p>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <Badge variant={feature.status === 'Active' ? 'default' : 'secondary'}>
                  {feature.status}
                </Badge>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleConfigure(feature.title)}
                >
                  Configure
                </Button>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default SecurityFeaturesGrid;
