import React from "react";
import {
  Building2,
  Users,
  CreditCard,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useTranslation } from "react-i18next";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronDown } from "lucide-react";
import { useLanguage } from "@relativity/shared/dist/i18n/LanguageProvider";

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  activeSection,
  onSectionChange,
}) => {
  const { t } = useLanguage();
  const [organizationOpen, setOrganizationOpen] = React.useState(false);

  const menuItems = [
    { id: "dashboard", label: t("nav.dashboard"), icon: BarChart3 },
    {
      id: "organizations",
      label: t("nav.organizations"),
      icon: Building2,
      submenu: [
        { id: "organizations", label: t("nav.organizations") },
        { id: "personnel", label: t("nav.personnel") },
      ],
    },
    { id: "roles", label: t("nav.roles"), icon: Shield },
    // { id: 'licenses', label: t('nav.licenses'), icon: CreditCard },
    // { id: 'security', label: t('nav.security'), icon: Shield },
    // { id: 'settings', label: t('nav.settings'), icon: Settings },
  ];

  React.useEffect(() => {
    if (
      activeSection === "personnel" ||
      activeSection === "organizations" ||
      activeSection === "roles"
    ) {
      setOrganizationOpen(true);
    }
  }, [activeSection]);

  return (
    <aside className="w-64 bg-sidebar border-r border-sidebar-border h-full">
      <nav className="p-4 space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;

          if (item.submenu) {
            return (
              <Collapsible
                key={item.id}
                open={organizationOpen}
                onOpenChange={setOrganizationOpen}>
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-between text-left",
                      activeSection === "organizations" ||
                        activeSection === "personnel"
                        ? "bg-sidebar-accent text-sidebar-accent-foreground hover:bg-sidebar-accent"
                        : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                    )}>
                    <div className="flex items-center">
                      <Icon className="mr-3 h-4 w-4" />
                      {item.label}
                    </div>
                    <ChevronDown
                      className={cn(
                        "h-4 w-4 transition-transform",
                        organizationOpen && "rotate-180"
                      )}
                    />
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="ml-6 space-y-1">
                  {item.submenu.map((subItem) => (
                    <Button
                      key={subItem.id}
                      variant="ghost"
                      className={cn(
                        "w-full justify-start text-left text-sm",
                        activeSection === subItem.id
                          ? "bg-sidebar-accent text-sidebar-accent-foreground hover:bg-sidebar-accent"
                          : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                      )}
                      onClick={() => onSectionChange(subItem.id)}>
                      {subItem.label}
                    </Button>
                  ))}
                </CollapsibleContent>
              </Collapsible>
            );
          }

          return (
            <Button
              key={item.id}
              variant="ghost"
              className={cn(
                "w-full justify-start text-left",
                activeSection === item.id
                  ? "bg-sidebar-accent text-sidebar-accent-foreground hover:bg-sidebar-accent"
                  : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
              )}
              onClick={() => onSectionChange(item.id)}>
              <Icon className="mr-3 h-4 w-4" />
              {item.label}
            </Button>
          );
        })}
      </nav>
    </aside>
  );
};

export default Sidebar;
