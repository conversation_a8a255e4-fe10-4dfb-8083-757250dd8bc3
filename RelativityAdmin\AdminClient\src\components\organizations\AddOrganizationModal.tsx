import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Building2 } from "lucide-react";
import { useCreateOrganization } from "@/hooks/useOrganizations";
import { useToast } from "@/hooks/use-toast";
import {
  organizationSchema,
  OrganizationFormData,
  CreateOrganizationData,
} from "@/lib/Schemas/Organization";
import { api } from "@/hooks/useMasters";
import CommonAutoComplete from "../autocomplete/CommonAutoComplete";
import { useLanguage } from "@relativity/shared/dist/i18n/LanguageProvider";

interface AddOrganizationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const AddOrganizationModal: React.FC<AddOrganizationModalProps> = ({
  open,
  onOpenChange,
}) => {
  const { toast } = useToast();
  const createOrganizationMutation = useCreateOrganization();
  const { t } = useLanguage();

  const form = useForm<OrganizationFormData>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      organizationName: "",
      domain: "",
      numberOfLicenses: 10,
      address: {
        streetAddress: "",
        city: "",
        state: "",
        zipCode: "",
        country: "United States",
      },
      description: "",
    },
  });

  const onSubmit = async (data: OrganizationFormData) => {
    try {
      await createOrganizationMutation.mutateAsync(
        data as CreateOrganizationData
      );
      toast({
        title: "Success",
        description: "Organization created successfully!",
      });
      form.reset();
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create organization. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5 text-primary" />
            <span>{t('organizations.addOrganizations')}</span>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="organizationName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('organizations.organizationName')}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('organizations.organizationNamePlaceHolder')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="domain"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('organizations.domain')}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('organizations.domainPlaceholder')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="numberOfLicenses"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('organizations.numberOfLicenses')}</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      placeholder="10"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseInt(e.target.value) || 0)
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">{t('organizations.addressInformation')}</h3>

              <FormField
                control={form.control}
                name="address.streetAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('organizations.streetAddress')}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('organizations.streetAddressPlaceholder')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="address.city"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel>{t('organizations.city')}</FormLabel>
                        <div className="relative z-50">
                          <CommonAutoComplete
                            type="city"
                            placeholder={t('organizations.cityPlaceholder')}
                            fetchFunction={(search, limit) =>
                              api.getCity(search, limit)
                            }
                            onSelect={(city) => {
                              form.setValue("address.city", city.CityName);
                            }}
                            handleClear={() => {
                              form.setValue("address.city", "");
                            }}
                          />
                        </div>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />

                <FormField
                  control={form.control}
                  name="address.state"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel>{t('organizations.state')}</FormLabel>
                        <div className="relative z-50">
                          <CommonAutoComplete
                            type="state"
                            placeholder={t('organizations.statePlaceholder')}
                            fetchFunction={(search, limit) =>
                              api.getState(search, limit)
                            }
                            onSelect={(state) => {
                              form.setValue("address.state", state.StateName);
                            }}
                            handleClear={() => {
                              form.setValue("address.state", "");
                            }}
                          />
                        </div>

                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="address.zipCode"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel>{t('organizations.zipcode')}</FormLabel>
                        <div className="relative z-0">
                          <CommonAutoComplete
                            type="zip"
                            placeholder={t('organizations.zipcodePlaceholder')}
                            fetchFunction={(search, limit) =>
                              api.getZipCode(search, limit)
                            }
                            onSelect={(zip) =>
                              form.setValue("address.zipCode", zip.ZipCode)
                            }
                            handleClear={() =>
                              form.setValue("address.zipCode", "")
                            }
                          />
                        </div>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
                <FormField
                  control={form.control}
                  name="address.country"
                  render={({ field }) => {
                    {
                      return (
                        <FormItem>
                          <FormLabel>{t('organizations.country')}</FormLabel>
                          <div className="relative z-0">
                            <CommonAutoComplete
                              type="country"
                              placeholder={t('organizations.countryPlaceholder')}
                              fetchFunction={(search, limit) =>
                                api.getCountry(search, limit)
                              }
                              onSelect={(country) =>
                                form.setValue(
                                  "address.country",
                                  country.label_en
                                )
                              }
                              handleClear={() =>
                                form.setValue("address.country", "")
                              }
                            />
                          </div>
                          <FormMessage />
                        </FormItem>
                      );
                    }
                  }}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('organizations.description')}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t('organizations.descriptionPlaceholder')}
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}>
                {t('common.cancel')}
              </Button>
              <Button
                type="submit"
                disabled={createOrganizationMutation.isPending}>
                {createOrganizationMutation.isPending
                  ? t('organizations.creating')
                  : t("organizations.addOrganizations")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddOrganizationModal;
