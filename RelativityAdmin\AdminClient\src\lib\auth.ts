import { Person } from "./Schemas/Personnel";

export interface AuthState {
  isAuthenticated: boolean;
  user: Person | null;
  accessToken: string | null;
  refreshToken: string | null;
}

export function getStoredAuth(): AuthState {
  try {
    const accessToken = localStorage.getItem("access_token");
    const userStr = localStorage.getItem("auth_user");
    const refreshToken = localStorage.getItem("refresh_token");

    if (accessToken && userStr) {
      const user = JSON.parse(userStr);
      return {
        isAuthenticated: true,
        user,
        accessToken,
        refreshToken,
      };
    }
  } catch (error) {
    console.error("Failed to parse stored auth:", error);
  }

  return {
    isAuthenticated: false,
    user: null,
    accessToken: null,
    refreshToken: null,
  };
}

export function storeAuth(
  user: Person | null,
  accessToken: string,
  refreshToken: string
): void {
  localStorage.setItem("auth_user", JSON.stringify(user));
  localStorage.setItem("access_token", accessToken);
  localStorage.setItem("refresh_token", refreshToken);
}

export function clearAuth(): void {
  localStorage.clear();
}

export function getAuthHeader(): Record<string, string> {
  const { accessToken } = getStoredAuth();
  return accessToken ? { Authorization: `Bearer ${accessToken}` } : {};
}
