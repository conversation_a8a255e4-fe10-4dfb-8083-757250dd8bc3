// src/lib/axiosInterceptor.ts

import axios, { InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

/**
 * Axios instance with interceptors for Authorization headers and error handling.
 * Usage: import { apiClient } from '@/lib/axiosInterceptor' and use apiClient instead of fetch.
 */

// Create axios instance
export const apiClient = axios.create({
  timeout: 10000, // 10 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

console.log('🚀 Axios instance created with interceptors');

// Request interceptor to add Authorization header
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = localStorage.getItem("access_token");

    console.log('🔍 Axios Interceptor Debug:', {
      url: config.url,
      method: config.method,
      hasToken: !!token,
      tokenPreview: token ? `${token.substring(0, 20)}...` : 'No token',
      headers: config.headers
    });

    if (token) {
      // Ensure headers object exists
      if (!config.headers) {
        config.headers = {} as any;
      }

      // Set the Authorization header
      config.headers.Authorization = `Bearer ${token}`;

      console.log('✅ Authorization header set:', config.headers.Authorization?.substring(0, 30) + '...');
    } else {
      console.warn('⚠️ No access token found in localStorage');
    }

    return config;
  },
  (error: AxiosError) => {
    console.error('❌ Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log('✅ API Response:', {
      url: response.config.url,
      status: response.status,
      statusText: response.statusText
    });
    return response;
  },
  (error: AxiosError) => {
    console.error('❌ API Error:', {
      url: error.config?.url,
      status: error.response?.status,
      statusText: error.response?.statusText,
      message: error.message,
      responseData: error.response?.data
    });

    // Handle common errors
    if (error.response?.status === 401) {
      console.warn('🔒 Unauthorized - clearing tokens and redirecting to login');
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem("access_token");
      localStorage.removeItem("refresh_token");
      localStorage.removeItem("auth_user");

      // Redirect to login page
      window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

/**
 * Legacy function for backward compatibility
 * @deprecated Use apiClient directly instead
 */
export function setupaxiosInterceptor() {
  console.warn('setupaxiosInterceptor is deprecated. Axios interceptors are automatically configured.');
}

/**
 * Test function to verify token and interceptor functionality
 */
export function testTokenInterceptor() {
  const token = localStorage.getItem("access_token");
  console.log('🧪 Token Test:', {
    hasToken: !!token,
    tokenLength: token?.length || 0,
    tokenPreview: token ? `${token.substring(0, 20)}...` : 'No token found',
    localStorage: {
      access_token: !!localStorage.getItem("access_token"),
      refresh_token: !!localStorage.getItem("refresh_token"),
      auth_user: !!localStorage.getItem("auth_user")
    }
  });

  return {
    hasToken: !!token,
    token: token
  };
}
