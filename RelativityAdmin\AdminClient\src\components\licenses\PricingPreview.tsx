
import React from 'react';

interface PricingPreviewProps {
  cycleLength: string;
  pricePerUser: number;
  minimumUsers: number;
}

const PricingPreview: React.FC<PricingPreviewProps> = ({
  cycleLength,
  pricePerUser,
  minimumUsers,
}) => {
  const getCycleMultiplier = (cycle: string) => {
    switch (cycle) {
      case 'quarterly': return 3;
      case 'annual': return 12;
      default: return 1;
    }
  };

  return (
    <div className="bg-blue-50 p-4 rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
        <div>
          <div className="text-2xl font-bold text-blue-600">
            ${pricePerUser || 0}
          </div>
          <p className="text-sm text-gray-600">per user/month</p>
        </div>
        <div>
          <div className="text-2xl font-bold text-purple-600">
            ${(pricePerUser || 0) * getCycleMultiplier(cycleLength)}
          </div>
          <p className="text-sm text-gray-600">per user/{cycleLength}</p>
        </div>
        <div>
          <div className="text-2xl font-bold text-green-600">
            ${(minimumUsers || 0) * (pricePerUser || 0) * getCycleMultiplier(cycleLength)}
          </div>
          <p className="text-sm text-gray-600">minimum {cycleLength} package</p>
        </div>
      </div>
    </div>
  );
};

export default PricingPreview;
