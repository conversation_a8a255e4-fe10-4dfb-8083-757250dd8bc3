import React, { useEffect, useState } from "react";
import { ReactSearchAutocomplete } from "react-search-autocomplete";

const CommonAutoComplete = ({
  type,
  placeholder,
  onSelect,
  handleClear,
  fetchFunction, // <-- you provide api.getCity or api.getZipCode etc.
}) => {
  const [items, setItems] = useState([]);

  // Fetch default list (first 50)
  useEffect(() => {
    const loadDefault = async () => {
      try {
        const defaultResults = await fetchFunction("", 50);
        const formatted = defaultResults.map((item, index) => ({
          id: item.id ?? index,
          name: getName(item),
          raw: item,
        }));
        setItems(formatted);
      } catch (err) {
        console.error(`Failed to load default ${type}`, err);
      }
    };
    loadDefault();
  }, [fetchFunction, type]);

  const getName = (item) => {
    if (type === "city") return item.CityName;
    if (type === "state") return item.StateName;
    if (type === "zip") return item.ZipCode;
    if (type === "country") return item.label_en;
    return item.name || "Unknown";
  };

  const handleOnSearch = async (query) => {
    if (!query) return;
    try {
      const results = await fetchFunction(query, 50);
      const formatted = results.map((item, index) => ({
        id: item.id ?? index,
        name: getName(item),
        raw: item,
      }));
      setItems(formatted);
    } catch (err) {
      console.error(`Search failed for ${type}:`, err);
      setItems([]);
    }
  };

  return (
    // <div className="relative z-0">
      <ReactSearchAutocomplete
        items={items}
        inputDebounce={300}
        onSearch={handleOnSearch}
        onSelect={(item) => onSelect(item.raw)}
        onClear={handleClear}
        autoFocus={false}
        maxResults={50}
        placeholder={placeholder}
        formatResult={(item) => <span>{item.name}</span>}
        styling={{
          border: "1px solid #ccc",
          borderRadius: "4px",
          boxShadow: "none",
          fontSize: "15px",
          zIndex: 9999, // Ensure this is very high
          height: "40px",
        }}
        className="custom-search"
      />
    // </div>
  );
};

export default CommonAutoComplete;
