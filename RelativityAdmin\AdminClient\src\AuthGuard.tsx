import { LoadingSpinner } from "./components/layout/LoadingSpinner";
import { useAuth } from "./hooks/useAuth";

const AuthGuard = ({ children }: { children: JSX.Element }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Don't render anything if not authenticated - redirect should happen in AuthProvider
  if (!isAuthenticated) {
    return null;
  }

  return children;
};

export default AuthGuard;
